package com.microsoft.mai.smartview.media;

import android.graphics.Bitmap;
import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaCodecList;
import android.media.MediaFormat;
import android.media.MediaMuxer;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.io.File;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CountDownLatch;

import io.reactivex.Completable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

/**
 * 视频编码器类
 * 负责将Bitmap图像序列编码为H.264格式视频文件
 * 使用Android MediaCodec进行硬件加速编码
 */
@SuppressWarnings({"deprecation"})
public class VideoEncoder {
    private static final String TAG = VideoEncoder.class.getSimpleName();
    // 超时时间，单位微秒
    private final static int TIMEOUT = 500000;

    // 编码完成回调接口实例
    private final IVideoEncoderCallback mCallback;
    // 输出视频文件
    private File mOutputFile;
    // 编码队列，用于存储待编码的Bitmap帧
    private Queue<Bitmap> mEncodeQueue = new ConcurrentLinkedQueue<>();
    // 视频编码器实例
    private MediaCodec mVideoMediaCodec;
    // 媒体混合器，用于将编码后的数据写入MP4文件
    private MediaMuxer mMediaMuxer;
    // 颜色格式，用于YUV转换
    private int mColorFormat;

    // 同步对象，用于等待新数据
    private final Object mSync = new Object();
    // 用于线程间等待新数据帧的信号量
    private CountDownLatch mNewPackageLatch;

    // 视频编码格式 - H.264/AVC
    private static final String MIME_TYPE = MediaFormat.MIMETYPE_VIDEO_AVC;
    // 视频比特率 - 16Mbps
    private static final int BIT_RATE = 16000000;
    // 帧率 - 每秒15帧
    private static final int FRAME_RATE = 15; // Frames per second
    // 关键帧间隔 - 每秒一个关键帧
    private static final int I_FRAME_INTERVAL = 1;

    // 视频轨道索引
    private int mVideoTrackIndex;

    // 标记是否没有更多帧需要处理
    private volatile boolean mNoMorePackage = false;
    // 标记是否中止编码过程
    private volatile boolean mAbort = false;

    /**
     * 视频编码器回调接口
     * 用于通知编码完成并返回编码后的文件
     */
    public interface IVideoEncoderCallback {
        /**
         * 编码完成回调方法
         * @param outputFile 编码输出的视频文件
         */
        void onEncodingComplete(File outputFile);
    }

    /**
     * 构造函数
     * @param callback 编码完成回调接口实例
     */
    public VideoEncoder(IVideoEncoderCallback callback) {
        mCallback = callback;
    }

    /**
     * 检查编码器是否已启动
     * @return 如果编码器已启动且未停止或中止，返回true
     */
    public boolean isEncodingStarted() {
        return (mVideoMediaCodec != null) && (mMediaMuxer != null) && !mNoMorePackage && !mAbort;
    }

    /**
     * 开始编码过程
     * 初始化编码器并在后台线程开始编码操作
     * @param width 视频宽度（像素）
     * @param height 视频高度（像素）
     * @param outputFile 编码输出的目标文件
     */
    public void startEncoding(int width, int height, @NonNull File outputFile) {
        mOutputFile = outputFile;

        // 获取输出文件的标准路径
        String outputFileString;
        try {
            outputFileString = outputFile.getCanonicalPath();
        } catch (IOException e) {
            Log.e(TAG, "Unable to get path for " + outputFile);
            return;
        }

        // 选择合适的编解码器
        MediaCodecInfo codecInfo = selectCodec();
        if (codecInfo == null) {
            Log.e(TAG, "Unable to find an appropriate codec for " + MIME_TYPE);
            return;
        }

        Log.i(TAG, "found codec: " + codecInfo.getName());

        // 选择颜色格式
        int colorFormat;
        try {
            colorFormat = selectColorFormat(codecInfo);
        } catch (Exception e) {
            // 如果选择失败，使用默认的YUV420半平面格式
            colorFormat = MediaCodecInfo.CodecCapabilities.COLOR_FormatYUV420SemiPlanar;
        }

        mColorFormat = colorFormat;

        // 创建视频编码器
        try {
            mVideoMediaCodec = MediaCodec.createByCodecName(codecInfo.getName());
        } catch (Exception e) {
            Log.e(TAG, "Unable to create MediaCodec " + e.getMessage());
            return;
        }

        // 创建并配置视频格式
        MediaFormat videoFormat = MediaFormat.createVideoFormat(MIME_TYPE, width, height);
        videoFormat.setInteger(MediaFormat.KEY_BIT_RATE, BIT_RATE);
        videoFormat.setInteger(MediaFormat.KEY_FRAME_RATE, FRAME_RATE);
        videoFormat.setInteger(MediaFormat.KEY_COLOR_FORMAT, colorFormat);
        videoFormat.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, I_FRAME_INTERVAL);
        videoFormat.setInteger(MediaFormat.KEY_MAX_INPUT_SIZE, 5 * 1024 * 1024);

        // 配置并启动编码器
        mVideoMediaCodec.configure(videoFormat, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);
        mVideoMediaCodec.start();

        // 创建媒体混合器
        try {
            mMediaMuxer = new MediaMuxer(outputFileString, MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4);
        } catch (IOException e) {
            Log.e(TAG,"MediaMuxer creation failed. " + e.getMessage());
            return;
        }

        Log.i(TAG, "Initialization complete. Starting encoder...");

        // 在IO线程执行编码操作
        Completable.fromAction(this::encode)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe();
    }

    /**
     * 停止编码过程
     * 设置停止标志并通知等待线程
     */
    public void stopEncoding() {
        if (mVideoMediaCodec == null || mMediaMuxer == null) {
            Log.e(TAG, "Failed to stop encoding since it never started");
            return;
        }
        Log.i(TAG, "Stopping encoding");

        // 设置停止标志
        mNoMorePackage = true;

        // 通知等待新数据的线程继续执行
        synchronized (mSync) {
            if ((mNewPackageLatch != null) && (mNewPackageLatch.getCount() > 0)) {
                mNewPackageLatch.countDown();
            }
        }
    }

    /**
     * 中止编码过程
     * 设置中止标志，清空队列并通知等待线程
     */
    public void abortEncoding() {
        if (mVideoMediaCodec == null || mMediaMuxer == null) {
            Log.e(TAG, "Failed to abort encoding since it never started");
            return;
        }
        Log.i(TAG, "Aborting encoding");

        // 设置停止和中止标志
        mNoMorePackage = true;
        mAbort = true;
        // 清空编码队列，丢弃所有帧
        mEncodeQueue = new ConcurrentLinkedQueue<>(); // Drop all frames

        // 通知等待新数据的线程继续执行
        synchronized (mSync) {
            if ((mNewPackageLatch != null) && (mNewPackageLatch.getCount() > 0)) {
                mNewPackageLatch.countDown();
            }
        }
    }

    /**
     * 将Bitmap帧添加到编码队列
     * @param bitmap 要编码的Bitmap图像帧
     */
    public void queueFrame(Bitmap bitmap) {
        if (mVideoMediaCodec == null || mMediaMuxer == null) {
            Log.e(TAG, "Failed to queue frame. Encoding not started");
            return;
        }

        Log.i(TAG, "Queueing frame");

        // 如果队列过大，防止内存溢出
        if (mEncodeQueue.size() >= 100) {
            Log.e(TAG, "Frame queue is full, just returns.");
            return;
        }

        // 添加帧到队列
        mEncodeQueue.add(bitmap);
        // 通知等待线程有新数据可用
        synchronized (mSync) {
            if ((mNewPackageLatch != null) && (mNewPackageLatch.getCount() > 0)) {
                mNewPackageLatch.countDown();
            }
        }
    }

    /**
     * 编码方法，在后台线程执行
     * 不断从队列获取Bitmap帧，转换为YUV格式并编码为H.264视频
     */
    private void encode() {
        Log.i(TAG, "Encoder started");

        // 当还有数据帧或队列不为空时继续编码
        while (!mNoMorePackage || !mEncodeQueue.isEmpty()) {
            // 从队列获取数据帧
            Bitmap bitmap = mEncodeQueue.poll();
            if (bitmap == null) {
                // 如果没有数据，等待新数据到达
                synchronized (mSync) {
                    mNewPackageLatch = new CountDownLatch(1);
                }

                try {
                    mNewPackageLatch.await();
                } catch (InterruptedException ignore) {
                }

                bitmap = mEncodeQueue.poll();
            }

            if (bitmap == null) continue;

            try {
                // 将Bitmap转换为NV21格式的YUV数据
                final byte[] frameData = getNV21(bitmap.getWidth(), bitmap.getHeight(), bitmap);

                // 获取输入缓冲区并填充数据
                int inputBufIndex = mVideoMediaCodec.dequeueInputBuffer(TIMEOUT);
                // 使用系统时间作为时间戳（微秒）
                long pts = System.nanoTime() / 1000L;
                if (inputBufIndex >= 0) {
                    final ByteBuffer inputBuffer = mVideoMediaCodec.getInputBuffer(inputBufIndex);
                    inputBuffer.clear();
                    inputBuffer.put(frameData);

                    // 将YUV数据加入编码队列
                    mVideoMediaCodec.queueInputBuffer(inputBufIndex, 0, frameData.length, pts,0);
                }

                // 获取并处理输出数据
                final MediaCodec.BufferInfo bufferInfo = new MediaCodec.BufferInfo();
                int encoderStatus = mVideoMediaCodec.dequeueOutputBuffer(bufferInfo, TIMEOUT);
                if (encoderStatus == MediaCodec.INFO_TRY_AGAIN_LATER) {
                    // 编码器暂时没有输出
                    Log.e(TAG, "No output from encoder available");
                } else if (encoderStatus == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED) {
                    // 编码器输出格式已改变，需要更新MediaMuxer
                    Log.i(TAG, "output format changed:" + mVideoMediaCodec.getOutputFormat());

                    MediaFormat newFormat = mVideoMediaCodec.getOutputFormat();
                    mVideoTrackIndex = mMediaMuxer.addTrack(newFormat);
                    mMediaMuxer.start();
                } else if (encoderStatus < 0) {
                    // 其他错误状态
                    Log.e(TAG, "unexpected result from encoder.dequeueOutputBuffer: " + encoderStatus);
                } else if (bufferInfo.size != 0) {
                    // 有效的编码数据
                    ByteBuffer encodedData = mVideoMediaCodec.getOutputBuffer(encoderStatus);
                    if (encodedData == null) {
                        Log.e(TAG, "encoderOutputBuffer " + encoderStatus + " was null");
                    } else {
                        // 设置缓冲区位置和限制
                        encodedData.position(bufferInfo.offset);
                        encodedData.limit(bufferInfo.offset + bufferInfo.size);

                        // 将编码后的H.264数据写入MP4文件
                        mMediaMuxer.writeSampleData(mVideoTrackIndex, encodedData, bufferInfo);
                        // 释放输出缓冲区
                        mVideoMediaCodec.releaseOutputBuffer(encoderStatus, false);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // 编码完成，释放资源
        release();

        // 处理编码结果
        if (mAbort) {
            // 如果编码被中止，删除输出文件
            boolean deleted = mOutputFile.delete();
            if (!deleted) {
                Log.e(TAG, "Failed to delete file: " + mOutputFile.getPath());
            }
        } else {
            // 编码成功完成，回调通知
            mCallback.onEncodingComplete(mOutputFile);
        }
    }

    /**
     * 释放编码器和媒体混合器资源
     */
    private void release() {
        if (mVideoMediaCodec != null) {
            try {
                mVideoMediaCodec.stop();
                mVideoMediaCodec.release();
            } catch (Exception e) {
                e.printStackTrace();
            }

            mVideoMediaCodec = null;
        }

        if (mMediaMuxer != null) {
            try {
                mMediaMuxer.stop();
                mMediaMuxer.release();
            } catch (Exception e) {
                e.printStackTrace();
            }

            mMediaMuxer = null;
        }
    }

    /**
     * 选择合适的视频编解码器
     * @return 找到的MediaCodecInfo对象，若未找到则返回null
     */
    @Nullable
    private static MediaCodecInfo selectCodec() {
        // 获取设备上所有编解码器的数量
        int numCodecs = MediaCodecList.getCodecCount();
        for (int i = 0; i < numCodecs; i++) {
            MediaCodecInfo codecInfo = MediaCodecList.getCodecInfoAt(i);
            // 跳过解码器，只查找编码器
            if (!codecInfo.isEncoder()) {
                continue;
            }

            // 检查编码器支持的媒体类型
            String[] types = codecInfo.getSupportedTypes();
            for (String type : types) {
                if (type.equalsIgnoreCase(MIME_TYPE)) {
                    return codecInfo;
                }
            }
        }

        return null;
    }

    /**
     * 从编解码器支持的颜色格式中选择一个可用的YUV格式
     * @param codecInfo 编解码器信息对象
     * @return 选中的颜色格式常量值
     */
    private static int selectColorFormat(@NonNull MediaCodecInfo codecInfo) {
        // 获取编解码器的功能描述
        MediaCodecInfo.CodecCapabilities capabilities = codecInfo
                .getCapabilitiesForType(MIME_TYPE);
        // 遍历所有支持的颜色格式
        for (int i = 0; i < capabilities.colorFormats.length; i++) {
            int colorFormat = capabilities.colorFormats[i];
            // 检查是否为已知的YUV格式
            if (isRecognizedFormat(colorFormat)) {
                return colorFormat;
            }
        }

        return 0;
    }

    /**
     * 检查颜色格式是否为已知的YUV格式
     * @param colorFormat 要检查的颜色格式常量值
     * @return 如果是已知的YUV格式则返回true
     */
    private static boolean isRecognizedFormat(int colorFormat) {
        switch (colorFormat) {
            case MediaCodecInfo.CodecCapabilities.COLOR_FormatYUV420Planar:
            case MediaCodecInfo.CodecCapabilities.COLOR_FormatYUV420PackedPlanar:
            case MediaCodecInfo.CodecCapabilities.COLOR_FormatYUV420SemiPlanar:
            case MediaCodecInfo.CodecCapabilities.COLOR_FormatYUV420PackedSemiPlanar:
            case MediaCodecInfo.CodecCapabilities.COLOR_TI_FormatYUV420PackedSemiPlanar:
                return true;
            default:
                return false;
        }
    }

    /**
     * 将Bitmap转换为NV21格式的YUV数据
     * @param inputWidth 图像宽度
     * @param inputHeight 图像高度
     * @param scaled 要转换的Bitmap对象
     * @return 转换后的NV21格式字节数组
     */
    @NonNull
    private byte[] getNV21(int inputWidth, int inputHeight, @NonNull Bitmap scaled) {
        // 获取Bitmap的ARGB像素数据
        int[] argb = new int[inputWidth * inputHeight];
        scaled.getPixels(argb, 0, inputWidth, 0, 0, inputWidth, inputHeight);

        // 创建YUV数据缓冲区（YUV420格式大小为width*height*3/2）
        byte[] yuv = new byte[inputWidth * inputHeight * 3 / 2];
        // 根据颜色格式选择不同的转换方法
        if (MediaCodecInfo.CodecCapabilities.COLOR_FormatYUV420Planar == mColorFormat) {
            // 转换为YUV420P平面格式
            encodeYUV420P(yuv, argb, inputWidth, inputHeight);
        } else {
            // 转换为YUV420SP（NV21）半平面格式
            encodeYUV420SP(yuv, argb, inputWidth, inputHeight);
        }

        // scaled.recycle();
        return yuv;
    }

    /**
     * 将ARGB数据转换为YUV420SP（NV21）格式
     * 在这种格式中，Y平面后跟交错的UV平面（UVUVUV...）
     * @param yuv420sp 输出的YUV数据数组
     * @param argb 输入的ARGB像素数据
     * @param width 图像宽度
     * @param height 图像高度
     */
    private void encodeYUV420SP(byte[] yuv420sp, int[] argb, int width, int height) {
        // 计算Y平面大小
        final int frameSize = width * height;

        // Y平面索引从0开始
        int yIndex = 0;
        // UV平面索引从frameSize开始
        int uvIndex = frameSize;

        // ARGB转YUV的参数
        int a, R, G, B, Y, U, V;
        int index = 0;
        for (int j = 0; j < height; j++) {
            for (int i = 0; i < width; i++) {
                // 从ARGB数据中提取各个分量
                a = (argb[index] & 0xff000000) >> 24; // a is not used obviously
                R = (argb[index] & 0xff0000) >> 16;
                G = (argb[index] & 0xff00) >> 8;
                B = (argb[index] & 0xff);

                // 使用BT.601标准公式进行RGB到YUV的转换
                Y = ((66 * R + 129 * G + 25 * B + 128) >> 8) + 16;
                U = ((-38 * R - 74 * G + 112 * B + 128) >> 8) + 128;
                V = ((112 * R - 94 * G - 18 * B + 128) >> 8) + 128;

                // 限制Y值在0-255范围内
                yuv420sp[yIndex++] = (byte) ((Y < 0) ? 0 : ((Y > 255) ? 255 : Y));
                // 对于UV平面，每2x2个像素采样一次
                if (j % 2 == 0 && index % 2 == 0) {
                    // 限制UV值在0-255范围内
                    yuv420sp[uvIndex++] = (byte) ((U < 0) ? 0 : ((U > 255) ? 255 : U));
                    yuv420sp[uvIndex++] = (byte) ((V < 0) ? 0 : ((V > 255) ? 255 : V));
                }

                index++;
            }
        }
    }

    /**
     * 将ARGB数据转换为YUV420P平面格式
     * 在这种格式中，Y平面、U平面和V平面是完全分离的
     * @param yuv420sp 输出的YUV数据数组
     * @param argb 输入的ARGB像素数据
     * @param width 图像宽度
     * @param height 图像高度
     */
    private void encodeYUV420P(byte[] yuv420sp, int[] argb, int width, int height) {
        // 计算Y平面大小
        final int frameSize = width * height;

        // Y平面索引从0开始
        int yIndex = 0;
        // U平面索引从frameSize开始
        int uIndex = frameSize;
        // V平面索引从frameSize+frameSize/4开始
        int vIndex = frameSize + frameSize/4;

        // ARGB转YUV的参数
        int a, R, G, B, Y, U, V;
        int index = 0;
        for (int j = 0; j < height; j++) {
            for (int i = 0; i < width; i++) {
                // 从ARGB数据中提取各个分量
                a = (argb[index] & 0xff000000) >> 24; // a is not used obviously
                R = (argb[index] & 0xff0000) >> 16;
                G = (argb[index] & 0xff00) >> 8;
                B = (argb[index] & 0xff) >> 0;

                // 使用BT.601标准公式进行RGB到YUV的转换
                Y = ((66 * R + 129 * G + 25 * B + 128) >> 8) + 16;
                U = ((-38 * R - 74 * G + 112 * B + 128) >> 8) + 128;
                V = ((112 * R - 94 * G - 18 * B + 128) >> 8) + 128;

                // 限制Y值在0-255范围内
                yuv420sp[yIndex++] = (byte) ((Y < 0) ? 0 : ((Y > 255) ? 255 : Y));
                // 对于UV平面，每2x2个像素采样一次
                if (j % 2 == 0 && index % 2 == 0) {
                    // 限制UV值在0-255范围内
                    yuv420sp[uIndex++] = (byte) ((U < 0) ? 0 : ((U > 255) ? 255 : U));
                    yuv420sp[vIndex++] = (byte) ((V < 0) ? 0 : ((V > 255) ? 255 : V));
                }

                index++;
            }
        }
    }
}
