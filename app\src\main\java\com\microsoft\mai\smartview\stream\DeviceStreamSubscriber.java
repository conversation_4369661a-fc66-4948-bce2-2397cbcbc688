package com.microsoft.mai.smartview.stream;

import android.util.Log;

import androidx.annotation.NonNull;

import java.io.IOException;
import java.net.URL;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;

/**
 * 设备流订阅器类
 * 负责管理设备流的订阅、分发和生命周期
 * 采用单例模式实现，支持多个监听器订阅同一数据流
 */
public class DeviceStreamSubscriber {
    public static final String TAG = "DeviceStreamSubscriber";

    /**
     * 设备流数据监听器接口
     * 用于接收流数据和错误通知
     */
    public interface DeviceStreamDataListener {
        /**
         * 当接收到数据时调用
         * @param data 接收到的字节数组数据
         */
        void onDataReceived(@NonNull byte[] data);
        
        /**
         * 当发生错误时调用
         * @param t 错误信息
         */
        void onError(@NonNull Throwable t);
    }

    /**
     * 自定义线程工厂类
     * 用于创建具有优先级的线程
     */
    private static class PriorityThreadFactory implements ThreadFactory {
        // 线程优先级
        private final int mPriority;

        /**
         * 构造函数
         * @param priority 线程优先级
         */
        public PriorityThreadFactory(int priority) {
            this.mPriority = priority;
        }

        /**
         * 创建新线程并设置优先级
         * @param r Runnable任务
         * @return 创建的线程
         */
        public Thread newThread(Runnable r) {
            Thread t = new Thread(r);
            t.setPriority(mPriority);
            return t;
        }
    }

    // 固定大小的线程池，使用最高优先级
    private final static ExecutorService mThreadPool = Executors.newFixedThreadPool(2, new PriorityThreadFactory(Thread.MAX_PRIORITY));

    // URL到监听器列表的映射，保存所有订阅关系
    private final Map<URL, CopyOnWriteArrayList<DeviceStreamDataListener>> mStreamListeners = new ConcurrentHashMap<>();
    // URL到流数据源的映射
    private final Map<URL, DeviceStreamSource> mStreamSources = new ConcurrentHashMap<>();
    // URL到流运行状态的映射
    private final Map<URL, Boolean> mStreamRunningStatus = new ConcurrentHashMap<>();

    // 单例实例
    private static DeviceStreamSubscriber instance;
    
    /**
     * 私有构造函数，确保单例模式
     */
    private DeviceStreamSubscriber() {
    }

    /**
     * 获取单例实例
     * 使用双重检查锁定确保线程安全
     * @return DeviceStreamSubscriber实例
     */
    public static DeviceStreamSubscriber getInstance() {
        if (instance == null) {
            synchronized (DeviceStreamSubscriber.class) {
                if (instance == null) {
                    instance = new DeviceStreamSubscriber();
                }
            }
        }

        return instance;
    }

    /**
     * 清除所有订阅者
     * 用于应用重置或退出时清理资源
     */
    public synchronized void clearAllSubscribers() {
        mStreamListeners.clear();
        mStreamSources.clear();
        mStreamRunningStatus.clear();
    }

    /**
     * 检查指定类型的流是否正在运行
     * @param isVideo 是否是视频流
     * @return 如果流正在运行返回true，否则返回false
     */
    public synchronized boolean isStreaming(boolean isVideo) {
        if (!mStreamRunningStatus.containsKey(isVideo)) {
            return false;
        }

        return mStreamRunningStatus.get(isVideo);
    }

    /**
     * 添加订阅者
     * @param url 设备流URL
     * @param listener 数据监听器
     */
    public synchronized void addSubscriber(@NonNull URL url, @NonNull DeviceStreamDataListener listener) {
        Log.i(TAG, "addSubscriber: " + url);

        CopyOnWriteArrayList<DeviceStreamDataListener> listeners = mStreamListeners.get(url);
        if (listeners == null) {
            listeners = new CopyOnWriteArrayList<>();
        }

        if (!listeners.contains(listener)) {
            listeners.add(listener);
        }

        mStreamListeners.put(url, listeners);
        onListenerChanges(url, listeners, true);
    }

    /**
     * 移除订阅者
     * @param url 设备流URL
     * @param listener 要移除的数据监听器
     */
    public synchronized void removeSubscriber(@NonNull URL url, @NonNull DeviceStreamDataListener listener) {
        Log.i(TAG, "removeSubscriber: " + url);

        final CopyOnWriteArrayList<DeviceStreamDataListener> listeners = mStreamListeners.get(url);
        if (listeners != null) {
            listeners.remove(listener);
        } else {
            return;
        }

        onListenerChanges(url, listeners, false);
    }

    /**
     * 处理监听器变化事件
     * 当第一个监听器添加时开始流处理，当最后一个监听器移除时停止流处理
     * @param url 设备流URL
     * @param listeners 监听器列表
     * @param isAdd 是否是添加操作
     */
    private void onListenerChanges(@NonNull URL url, @NonNull CopyOnWriteArrayList<DeviceStreamDataListener> listeners, boolean isAdd) {
        if (isAdd && listeners.size() == 1) {
            startStreaming(url);
        }

        if (!isAdd && listeners.isEmpty()) {
            stopStreaming(url);
        }
    }

    /**
     * 开始流数据处理
     * 创建数据源并在线程池中执行数据接收和分发
     * @param url 设备流URL
     */
    private void startStreaming(@NonNull URL url) {
        Log.i(TAG, "startStreaming: " + url);

        mThreadPool.execute(() -> {
            final DeviceStreamSource src = new DeviceStreamSource(url);
            try {

                mStreamSources.put(url, src);
                mStreamRunningStatus.put(url, true);
                src.connect();

                for (byte[] audio : src) {
                    final CopyOnWriteArrayList<DeviceStreamDataListener> listeners = mStreamListeners.get(url);
                    if (listeners == null || Boolean.FALSE.equals(mStreamRunningStatus.get(url))) {
                        break;
                    }

                    for (DeviceStreamDataListener listener : listeners) {
                        listener.onDataReceived(audio);
                    }
                }
            } catch (RuntimeException | IOException e) {
                Log.e(TAG, "Error occurred for streaming: " + url + ", error message:" + e.getMessage());
                e.printStackTrace();

                if (Boolean.TRUE.equals(mStreamRunningStatus.get(url))) {
                    final CopyOnWriteArrayList<DeviceStreamDataListener> listeners = mStreamListeners.get(url);
                    if (listeners != null) {
                        for (DeviceStreamDataListener listener : listeners) {
                            listener.onError(e);
                        }
                    }
                }
            }
        });
    }

    /**
     * 停止流数据处理
     * 关闭数据源并清理资源
     * @param url 设备流URL
     */
    private void stopStreaming(@NonNull URL url) {
        Log.i(TAG, "stopStreaming: " + url);

        final DeviceStreamSource source = mStreamSources.get(url);
        if (source != null) {
            mStreamRunningStatus.put(url, false);
            source.close();
            mStreamSources.remove(url);
        }
    }
}
