package com.microsoft.mai.smartview.media;

import android.annotation.SuppressLint;
import android.media.AudioFormat;
import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaExtractor;
import android.media.MediaFormat;
import android.media.MediaMuxer;
import android.util.Log;

import androidx.annotation.NonNull;

import com.microsoft.mai.smartview.constant.Constants;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CountDownLatch;

import io.reactivex.Completable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

/**
 * 音频编码器类
 * 负责将PCM格式的原始音频数据编码为AAC格式，并保存到文件
 * 使用Android MediaCodec进行硬件加速编码
 */
public class AudioEncoder {
    private static final String TAG = "AudioEncoder";
    // 超时时间，单位微秒
    private static final int TIMEOUT = 500000;

    // 音频采样率，从常量类获取
    private static final int SAMPLE_RATE = Constants.AUDIO_SAMPLE_RATE;
    // 通道数，单声道
    private static final int CHANNEL_COUNT = 1;
    // 比特率
    private static final int BIT_RATE = 32000;
    // 输入缓冲区大小 (512KB)
    private static final int INPUT_BUFFER = 512 * 1024;

    // 媒体编码器实例
    private MediaCodec mMediaCodec;

    // 输出文件和文件输出流
    private File mOutputFile;
    private FileOutputStream mOutputStream;

    // 编码队列，使用线程安全的并发队列
    private Queue<byte[]> mEncodeQueue = new ConcurrentLinkedQueue<>();
    // 标记是否没有更多数据包需要处理
    private volatile boolean mNoMorePackage = false;
    // 标记是否中止编码过程
    private volatile boolean mAbort = false;

    // 同步对象，用于等待新数据
    private final Object mSync = new Object();
    // 用于线程间等待新数据包的信号量
    private CountDownLatch mNewPackageLatch;

    // 编码完成回调接口实例
    private final IAudioEncoderCallback mCallback;

    /**
     * 音频编码器回调接口
     * 用于通知编码完成并返回编码后的文件
     */
    public interface IAudioEncoderCallback {
        /**
         * 编码完成回调方法
         * @param outputFile 编码输出的音频文件
         */
        void onEncodingComplete(File outputFile);
    }

    /**
     * 构造函数
     * @param callback 编码完成回调接口实例
     */
    public AudioEncoder(@NonNull IAudioEncoderCallback callback) {
        mCallback = callback;
    }

    /**
     * 开始编码过程
     * 初始化编码器并在后台线程开始编码操作
     * @param outFile 编码输出的目标文件
     */
    public void startEncoding(@NonNull File outFile) {
        mOutputFile = outFile;
        try {
            mOutputStream = new FileOutputStream(outFile);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }

        try {
            // 创建AAC音频编码器
            mMediaCodec = MediaCodec.createEncoderByType(MediaFormat.MIMETYPE_AUDIO_AAC);
        } catch(IOException e) {
            e.printStackTrace();
        }

        // 创建并配置媒体格式
        MediaFormat mediaFormat = MediaFormat.createAudioFormat(MediaFormat.MIMETYPE_AUDIO_AAC, SAMPLE_RATE, CHANNEL_COUNT);
        mediaFormat.setInteger(MediaFormat.KEY_BIT_RATE, BIT_RATE);
        mediaFormat.setInteger(MediaFormat.KEY_AAC_PROFILE, MediaCodecInfo.CodecProfileLevel.AACObjectLC);
        mediaFormat.setInteger(MediaFormat.KEY_CHANNEL_MASK, AudioFormat.CHANNEL_IN_MONO);
        mediaFormat.setInteger(MediaFormat.KEY_MAX_INPUT_SIZE, INPUT_BUFFER);

        // 配置并启动编码器
        mMediaCodec.configure(mediaFormat, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);
        mMediaCodec.start();

        // 在IO线程执行编码操作
        Completable.fromAction(this::encode)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe();
    }

    /**
     * 停止编码过程
     * 设置停止标志并通知等待线程
     */
    public void stopEncoding() {
        Log.i(TAG, "Stopping encoding");

        if (mMediaCodec == null) {
            Log.e(TAG, "Encoding not started");
            return;
        }

        // 设置停止标志
        mNoMorePackage = true;
        // 通知等待新数据的线程继续执行
        synchronized (mSync) {
            if ((mNewPackageLatch != null) && (mNewPackageLatch.getCount() > 0)) {
                mNewPackageLatch.countDown();
            }
        }
    }

    /**
     * 中止编码过程
     * 设置中止标志，清空队列并通知等待线程
     */
    public void abortEncoding() {
        Log.i(TAG, "Aborting encoding");

        if (mMediaCodec == null) {
            Log.e(TAG, "Encoding not started");
            return;
        }

        // 设置停止和中止标志
        mNoMorePackage = true;
        mAbort = true;
        // 清空编码队列
        mEncodeQueue = new ConcurrentLinkedQueue<>();

        // 通知等待新数据的线程继续执行
        synchronized (mSync) {
            if ((mNewPackageLatch != null) && (mNewPackageLatch.getCount() > 0)) {
                mNewPackageLatch.countDown();
            }
        }
    }

    /**
     * 将音频数据添加到编码队列
     * @param audioData 要编码的音频数据字节数组
     */
    public void queue(@NonNull byte[] audioData) {
        Log.i(TAG, "Queueing audio bytes");

        if (mMediaCodec == null) {
            Log.e(TAG, "Encoding not started");
            return;
        }

        // 添加数据到队列
        mEncodeQueue.add(audioData);
        // 通知等待线程有新数据可用
        synchronized (mSync) {
            if ((mNewPackageLatch != null) && (mNewPackageLatch.getCount() > 0)) {
                mNewPackageLatch.countDown();
            }
        }
    }

    /**
     * 编码方法，在后台线程执行
     * 不断从队列获取数据，编码后写入文件
     */
    private void encode() {
        Log.i(TAG, "encode");

        if (mMediaCodec == null) {
            Log.e(TAG, "Encoding not started");
            return;
        }

        final MediaCodec.BufferInfo bufferInfo = new MediaCodec.BufferInfo();
        // 当还有数据包或队列不为空时继续编码
        while (!mNoMorePackage || !mEncodeQueue.isEmpty()) {
            // 从队列获取数据
            byte[] audioData = mEncodeQueue.poll();
            if (audioData == null) {
                // 如果没有数据，等待新数据到达
                synchronized (mSync) {
                    mNewPackageLatch = new CountDownLatch(1);
                }

                try {
                    mNewPackageLatch.await();
                } catch (InterruptedException ignore) {
                }

                audioData = mEncodeQueue.poll();
            }

            if (audioData == null) continue;

            try {
                // 获取输入缓冲区并填充数据
                int inputBufferIndex = mMediaCodec.dequeueInputBuffer(TIMEOUT);
                if (inputBufferIndex >= 0) {
                    ByteBuffer inputBuffer = mMediaCodec.getInputBuffer(inputBufferIndex);
                    inputBuffer.clear();
                    inputBuffer.limit(audioData.length);
                    inputBuffer.put(audioData);
                    mMediaCodec.queueInputBuffer(inputBufferIndex, 0, audioData.length, 0,
                            audioData.length == 0 ? MediaCodec.BUFFER_FLAG_END_OF_STREAM : 0);
                }

                // 获取并处理输出数据
                int waitTime = audioData.length == 0 ? -1 : 0;
                int outputBufferIndex = mMediaCodec.dequeueOutputBuffer(bufferInfo, waitTime);
                while(outputBufferIndex >= 0) {
                    if ((bufferInfo.flags & MediaCodec.BUFFER_FLAG_END_OF_STREAM) != 0) {
                        break;
                    }

                    // 计算输出包大小
                    int outPacketSize = bufferInfo.size + 7;

                    // 获取编码后的音频数据
                    ByteBuffer outputBuffer = mMediaCodec.getOutputBuffer(outputBufferIndex);
                    outputBuffer.position(bufferInfo.offset);
                    outputBuffer.limit(bufferInfo.offset + bufferInfo.size);

                    // 创建包含ADTS头的完整AAC数据包
                    byte []chunkAudio = new byte[outPacketSize];
                    addADTStoPacket(chunkAudio, outPacketSize);
                    outputBuffer.get(chunkAudio, 7, bufferInfo.size);

                    try {
                        // 将编码后的数据写入文件
                        mOutputStream.write(chunkAudio, 0, chunkAudio.length);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }

                    // 重置缓冲区位置并释放
                    outputBuffer.position(bufferInfo.offset);
                    mMediaCodec.releaseOutputBuffer(outputBufferIndex, false);
                    outputBufferIndex = mMediaCodec.dequeueOutputBuffer(bufferInfo, waitTime);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error occurred: " + e.getMessage());
                e.printStackTrace();
            }
        }

        // 编码完成，释放资源
        release();

        // 处理编码结果
        if (mAbort) {
            // 如果编码被中止，删除输出文件
            boolean deleted = mOutputFile.delete();
            if (!deleted) {
                Log.e(TAG, "Failed to delete file: " + mOutputFile.getPath());
            }
        } else {
            // 编码成功完成，回调通知
            Log.i(TAG, "encoding complete");
            mCallback.onEncodingComplete(mOutputFile);
        }
    }

    /**
     * 向AAC音频数据包添加ADTS头信息
     * ADTS(Audio Data Transport Stream)是AAC音频的传输流格式
     * @param packet 要添加头信息的数据包
     * @param packetLen 数据包总长度（包括头信息）
     */
    private void addADTStoPacket(@NonNull byte[] packet, int packetLen) {
        int profile = 2;  // AAC LC
        int freqIdx = 8;  // 16KHz
        int chanCfg = 1;  // 单声道

        // 生成ADTS头信息
        packet[0] = (byte) 0xFF;
        packet[1] = (byte) 0xF1;
        packet[2] = (byte) (((profile - 1) << 6) + (freqIdx << 2) + (chanCfg >> 2));
        packet[3] = (byte) (((chanCfg & 3) << 6) + (packetLen >> 11));
        packet[4] = (byte) ((packetLen & 0x7FF) >> 3);
        packet[5] = (byte) (((packetLen & 7) << 5) + 0x1F);
        packet[6] = (byte) 0xFC;
    }

    /**
     * 释放编码器资源并关闭输出流
     */
    public void release() {
        Log.i(TAG, "release");

        if (mMediaCodec != null) {
            try {
                mMediaCodec.stop();
                mMediaCodec.release();
            } catch (Exception ignore) {}
            mMediaCodec = null;
        }

        if (mOutputStream != null) {
            try {
                mOutputStream.flush();
                mOutputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            mOutputStream = null;
        }
    }

    /**
     * 合并音频和视频文件
     * 将单独的音频和视频文件合并为一个多媒体文件
     * @param audioFile 音频文件路径
     * @param videoFile 视频文件路径
     * @param outFile 输出文件路径
     * @return 合并是否成功
     */
    @SuppressLint("WrongConstant")
    public static boolean combineAudioToVideo(String audioFile, String videoFile, String outFile) {
        MediaExtractor videoExtractor = new MediaExtractor();
        MediaExtractor audioExtractor = new MediaExtractor();

        try {
            // 设置输入源
            videoExtractor.setDataSource(videoFile);
            audioExtractor.setDataSource(audioFile);
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }

        try {
            // 查找视频和音频轨道
            int videoTrackIndex = -1;
            for (int i = 0; i < videoExtractor.getTrackCount(); i++) {
                MediaFormat format = videoExtractor.getTrackFormat(i);
                String mime = format.getString(MediaFormat.KEY_MIME);
                if (mime.startsWith("video/")) {
                    videoTrackIndex = i;
                    break;
                }
            }

            int audioTrackIndex = -1;
            for (int i = 0; i < audioExtractor.getTrackCount(); i++) {
                MediaFormat format = audioExtractor.getTrackFormat(i);
                String mime = format.getString(MediaFormat.KEY_MIME);
                if (mime.startsWith("audio/")) {
                    audioTrackIndex = i;
                    break;
                }
            }

            if (videoTrackIndex < 0 || audioTrackIndex < 0) {
                return false;
            }

            // 获取媒体格式
            MediaFormat videoFormat = videoExtractor.getTrackFormat(videoTrackIndex);
            MediaFormat audioFormat = audioExtractor.getTrackFormat(audioTrackIndex);

            // 选择轨道
            videoExtractor.selectTrack(videoTrackIndex);
            audioExtractor.selectTrack(audioTrackIndex);

            // 创建媒体混合器
            MediaMuxer mediaMuxer = new MediaMuxer(outFile, MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4);
            int writeVideoTrackIndex = mediaMuxer.addTrack(videoFormat);
            int writeAudioTrackIndex = mediaMuxer.addTrack(audioFormat);
            mediaMuxer.start();

            // 写入视频数据
            ByteBuffer byteBuffer = ByteBuffer.allocate(1024 * 1024);
            MediaCodec.BufferInfo bufferInfo = new MediaCodec.BufferInfo();

            // 处理视频轨道
            videoExtractor.seekTo(0, MediaExtractor.SEEK_TO_CLOSEST_SYNC);
            while (true) {
                int sampleSize = videoExtractor.readSampleData(byteBuffer, 0);
                if (sampleSize < 0) {
                    break;
                }

                bufferInfo.size = sampleSize;
                bufferInfo.offset = 0;
                bufferInfo.flags = videoExtractor.getSampleFlags();
                bufferInfo.presentationTimeUs = videoExtractor.getSampleTime();

                mediaMuxer.writeSampleData(writeVideoTrackIndex, byteBuffer, bufferInfo);
                videoExtractor.advance();
            }

            // 处理音频轨道
            audioExtractor.seekTo(0, MediaExtractor.SEEK_TO_CLOSEST_SYNC);
            while (true) {
                int sampleSize = audioExtractor.readSampleData(byteBuffer, 0);
                if (sampleSize < 0) {
                    break;
                }

                bufferInfo.size = sampleSize;
                bufferInfo.offset = 0;
                bufferInfo.flags = audioExtractor.getSampleFlags();
                bufferInfo.presentationTimeUs = audioExtractor.getSampleTime();

                mediaMuxer.writeSampleData(writeAudioTrackIndex, byteBuffer, bufferInfo);
                audioExtractor.advance();
            }

            // 停止并释放资源
            mediaMuxer.stop();
            mediaMuxer.release();
            videoExtractor.release();
            audioExtractor.release();

            return true;
        } catch (Exception e) {
            Log.e(TAG, "Muxing error occurred: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
}
