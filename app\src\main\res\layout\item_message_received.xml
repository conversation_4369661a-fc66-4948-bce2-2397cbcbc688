<?xml version="1.0" encoding="utf-8"?>
<!-- 
    接收消息的列表项布局
    用于在聊天列表中显示接收到的消息
    显示在屏幕左侧，带有特定的接收消息气泡样式
-->
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="8dp"
    android:layout_marginTop="16dp">

    <!-- 消息卡片容器，使用透明背景 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/text_message_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:cardBackgroundColor="@android:color/transparent"
        app:cardCornerRadius="0dp"
        app:cardElevation="0dp"
        app:cardPreventCornerOverlap="false"
        app:cardUseCompatPadding="true"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 消息文本内容，使用接收消息气泡背景 -->
        <TextView
            android:id="@+id/text_message_body"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:background="@drawable/bubble_bkg_incoming"
            android:maxWidth="320dp"
            android:paddingLeft="12dp"
            android:paddingTop="8dp"
            android:paddingRight="12dp"
            android:paddingBottom="12dp"
            android:textColor="?attr/colorOnSecondaryFixed"
            android:textSize="20sp" />
    </androidx.cardview.widget.CardView>
</androidx.constraintlayout.widget.ConstraintLayout>