package com.microsoft.mai.smartview.media;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Environment;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.microsoft.mai.smartview.utils.RandomUtils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;

/**
 * Bitmap图像转JPEG工具类
 * 用于处理位图图像的保存和转换操作
 * 提供将Bitmap保存为JPEG文件和转换为字节数组的功能
 */
public class BitmapToJPEG {
    // 定义根路径为设备的DCIM目录
    private final static String RootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM).toString();

    /**
     * 将Bitmap保存为JPEG文件到DCIM/SmartView/images目录
     * 并通知媒体库扫描新文件，使其在相册应用中可见
     *
     * @param context 上下文对象，用于发送媒体扫描广播
     * @param bitmap 要保存的Bitmap图像
     * @return 保存成功则返回文件的绝对路径，失败则返回null
     */
    @Nullable
    public static String saveBitmap(@NonNull Context context, @NonNull Bitmap bitmap) {
        try {
            // 创建保存图像的目录
            File myDir = new File(RootPath + "/SmartView/images");

            // 使用当前时间生成唯一的文件名
            String name = "Image-" + RandomUtils.getCurrentTime() + ".jpg";
            File file = new File(myDir, name);

            // 将Bitmap压缩为JPEG并写入文件
            FileOutputStream out = new FileOutputStream(file);
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, out);

            // 确保所有数据写入文件并关闭流
            out.flush();
            out.close();

            // 发送广播通知媒体库扫描新文件
            final Intent mediaScanIntent = new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE);
            mediaScanIntent.setData(Uri.fromFile(file));
            context.sendBroadcast(mediaScanIntent);

            return file.getAbsolutePath();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    /**
     * 将Bitmap转换为WebP格式的字节数组
     * 使用WebP格式可获得较好的压缩率，适合网络传输
     *
     * @param bitmap 要转换的Bitmap图像
     * @return 转换成功则返回WebP格式的字节数组，失败则返回null
     */
    @Nullable
    public static byte[] getBytes(@NonNull Bitmap bitmap) {
        try {
            // 使用ByteArrayOutputStream将Bitmap压缩为WebP格式
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            bitmap.compress(Bitmap.CompressFormat.WEBP_LOSSY, 90, outputStream);
            return outputStream.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }
}
