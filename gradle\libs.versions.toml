[versions]
agp = "8.5.0"
ffmpegKitFull = "6.0-2"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
appcompat = "1.7.0"
material = "1.12.0"
constraintlayout = "2.1.4"
lifecycleLivedataKtx = "2.8.3"
lifecycleViewmodelKtx = "2.8.3"
multidex = "1.0.3"
navigationFragment = "2.7.7"
navigationUi = "2.7.7"
okhttp = "4.12.0"
core = "1.13.1"
rxandroid = "2.1.1"
websocket = "1.5.7"

[libraries]
ffmpeg-kit-full = { module = "com.arthenica:ffmpeg-kit-full", version.ref = "ffmpegKitFull" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
ext-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
lifecycle-livedata-ktx = { group = "androidx.lifecycle", name = "lifecycle-livedata-ktx", version.ref = "lifecycleLivedataKtx" }
lifecycle-viewmodel-ktx = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-ktx", version.ref = "lifecycleViewmodelKtx" }
multidex = { module = "com.android.support:multidex", version.ref = "multidex" }
navigation-fragment = { group = "androidx.navigation", name = "navigation-fragment", version.ref = "navigationFragment" }
navigation-ui = { group = "androidx.navigation", name = "navigation-ui", version.ref = "navigationUi" }
okhttp = { group = "com.squareup.okhttp3", name = "okhttp", version.ref = "okhttp" }
rxandroid = { module = "io.reactivex.rxjava2:rxandroid", version.ref = "rxandroid" }
websocket = { group = "org.java-websocket", name = "Java-WebSocket", version.ref = "websocket" }
core = { group = "androidx.core", name = "core", version.ref = "core" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }

