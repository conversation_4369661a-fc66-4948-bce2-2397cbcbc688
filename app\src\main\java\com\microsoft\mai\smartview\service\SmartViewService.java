package com.microsoft.mai.smartview.service;

import android.Manifest;
import android.app.Service;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.le.BluetoothLeScanner;
import android.bluetooth.le.ScanCallback;
import android.bluetooth.le.ScanFilter;
import android.bluetooth.le.ScanResult;
import android.bluetooth.le.ScanSettings;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Build;
import android.os.Bundle;
import android.os.HandlerThread;
import android.os.IBinder;
import android.os.Looper;
import android.os.Message;
import android.os.Messenger;
import android.os.RemoteException;
import android.os.SystemClock;
import android.util.Log;
import android.os.Handler;
import android.os.Process;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.annotation.RequiresPermission;
import androidx.core.app.ActivityCompat;

import com.microsoft.mai.smartview.constant.Constants;
import com.microsoft.mai.smartview.utils.StringUtils;
import com.microsoft.mai.smartview.wifi.WiFiApManager;

import org.java_websocket.framing.Framedata;
import org.java_websocket.handshake.ClientHandshake;
import org.java_websocket.server.WebSocketServer;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.UnknownHostException;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 智能视图服务类
 * 作为后台服务运行，提供蓝牙设备扫描、WiFi接入点管理和WebSocket通信功能
 * 通过Messenger机制与Activity进行进程间通信
 */
public final class SmartViewService extends Service {
    private final static String TAG = "SmartViewService";

    // Messenger对象，用于与Activity通信
    private Messenger mMessenger;
    // 后台线程，处理耗时操作
    private HandlerThread mHandlerThread;

    /**
     * 异步结果回调接口
     * 用于各种异步操作完成后返回结果
     */
    private interface AsyncResult {
        void onResult(@NonNull Object object);
    }

    // 最后一次ping时间戳，用于检测设备连接状态
    private static long lastPingTimestamp = 0;
    // WebSocket服务器实例
    private static SimpleWebSocketServer webSocketServer = null;

    /**
     * 检查远程设备是否已断开连接
     * 通过比较上次ping时间与当前时间的差值判断
     * @return 如果超过心跳超时时间则返回true，表示已断开
     */
    private static boolean checkRemoteDisconnected() {
        if (lastPingTimestamp == 0) {
            return false;
        }

        return (SystemClock.elapsedRealtime() - lastPingTimestamp) >= Constants.HEARTBEAT_DELAY;
        // return false;
    }

    /**
     * 简单WebSocket服务器类
     * 处理与智能设备的WebSocket通信
     */
    public static class SimpleWebSocketServer extends WebSocketServer {
        // 结果回调接口
        private final AsyncResult callback;

        /**
         * 构造函数
         * @param port 服务器监听端口
         * @param result 结果回调接口
         * @throws UnknownHostException 如果无法绑定到指定端口
         */
        public SimpleWebSocketServer(int port, @NonNull AsyncResult result) throws UnknownHostException {
            super(new InetSocketAddress("0.0.0.0", port));
            callback = result;
            lastPingTimestamp = SystemClock.elapsedRealtime();
        }

        /**
         * 连接打开回调
         * 当有客户端连接到服务器时调用
         */
        @Override
        public void onOpen(@NonNull org.java_websocket.WebSocket conn, ClientHandshake handshake) {
            Log.i(TAG, "onOpen called");

            // 请求客户端IP和MAC地址
            conn.send("get_ip_mac");
            lastPingTimestamp = SystemClock.elapsedRealtime();
        }

        /**
         * 连接关闭回调
         * 当客户端断开连接时调用
         */
        @Override
        public void onClose(org.java_websocket.WebSocket conn, int code, String reason, boolean remote) {
            Log.i(TAG, "onClose called");
            lastPingTimestamp = 0;
        }

        /**
         * WebSocket Ping回调
         * 用于保持连接和检测连接状态
         */
        @Override
        public void onWebsocketPing(org.java_websocket.WebSocket conn, Framedata f) {
            super.onWebsocketPing(conn, f);
            Log.i(TAG, "onWebsocketPing called ");
            lastPingTimestamp = SystemClock.elapsedRealtime();
        }

        /**
         * WebSocket Pong回调
         * 响应Ping请求的回应
         */
        @Override
        public void onWebsocketPong(org.java_websocket.WebSocket conn, Framedata f) {
            super.onWebsocketPong(conn, f);
            Log.i(TAG, "onWebsocketPong called ");
        }

        /**
         * 消息接收回调 - 文本消息
         * 处理从客户端接收的JSON格式文本消息
         */
        @Override
        public void onMessage(org.java_websocket.WebSocket conn, String message) {
            Log.i(TAG, "onMessage called, msg " + message);
            try {
                // 解析JSON消息并通过回调返回
                JSONObject json = new JSONObject(message);
                callback.onResult(json);
            } catch (JSONException e) {
                e.printStackTrace();
            }

            // 更新最后通信时间戳
            lastPingTimestamp = SystemClock.elapsedRealtime();
        }

        /**
         * 消息接收回调 - 二进制消息
         * 处理从客户端接收的二进制数据
         */
        @Override
        public void onMessage(org.java_websocket.WebSocket conn, ByteBuffer message) {
            Log.i(TAG, "onMessage called, byte message");
            lastPingTimestamp = SystemClock.elapsedRealtime();
        }

        /**
         * 错误回调
         * 处理WebSocket连接过程中的错误
         */
        @Override
        public void onError(org.java_websocket.WebSocket conn, Exception ex) {
            Log.i(TAG, "onError called");
            if (ex != null) {
                ex.printStackTrace();
            }

            if (conn != null) {
                Log.e(TAG, "onError but connection still returns");
            }

            // 重置时间戳，标记连接断开
            lastPingTimestamp = 0;
        }

        /**
         * 服务器启动回调
         * 当WebSocket服务器成功启动时调用
         */
        @Override
        public void onStart() {
            Log.i(TAG, "Websocket Server onStart called");
            // 设置连接超时检测
            setConnectionLostTimeout(0);
            setConnectionLostTimeout(100);

            lastPingTimestamp = SystemClock.elapsedRealtime();
        }
    }

    /**
     * 服务处理器类
     * 在后台线程处理来自Activity的消息请求
     */
    private final class ServiceHandler extends Handler {
        /**
         * 构造函数
         * @param looper 处理消息的Looper
         */
        public ServiceHandler(Looper looper) {
            super(looper);
        }

        /**
         * 消息处理方法
         * 根据消息类型执行不同的操作
         */
        @RequiresPermission(allOf = {
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.NEARBY_WIFI_DEVICES,
                Manifest.permission.BLUETOOTH_SCAN,
        })
        @RequiresApi(api = Build.VERSION_CODES.Q)
        @Override
        public void handleMessage(@NonNull Message msg) {
            // 获取消息发送者，用于回复
            Messenger replyTo = msg.replyTo;

            switch (msg.what) {
                case Constants.EVENT_BLE_SCAN: {
                    // 蓝牙设备扫描事件
                    final int what = msg.what;
                    scanBlueDevices(object -> {
                        String ssid = (String) object;
                        if (ssid != null && ssid.length() == 13) {
                            // 找到有效设备，发送回复
                            Message replyMsg = Message.obtain();
                            replyMsg.what = what;
                            Bundle data = new Bundle();
                            replyMsg.obj = data;

                            data.putString("ssid", ssid);
                            try {
                                replyTo.send(replyMsg);
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                    break;
                }

                case Constants.EVENT_SETUP_WIFI_AP: {
                    // 设置WiFi接入点事件
                    Message replyMsg = Message.obtain();
                    replyMsg.what = msg.what;
                    replyMsg.arg1 = msg.arg1;
                    Bundle data = new Bundle();
                    replyMsg.obj = data;

                    String hostSsid = (String) msg.obj;

                    // 启用WiFi接入点
                    setWifiApEnabled(true, hostSsid, (ssid, password) -> {
                        // 成功回调
                        data.putBoolean("success", true);
                        data.putString("ssid", ssid);
                        data.putString("password", password);
                        try {
                            replyTo.send(replyMsg);
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                    }, (failureCode, e) -> {
                        // 失败回调
                        data.putBoolean("success", false);
                        data.putString("ssid", "");
                        data.putString("password", "");
                        try {
                            replyTo.send(replyMsg);
                        } catch (RemoteException ex) {
                            ex.printStackTrace();
                        }
                    });
                    break;
                }

                case Constants.EVENT_STOP_WIFI_AP: {
                    // 停止WiFi接入点事件
                    setWifiApDisabled();
                    Message replyMsg = Message.obtain();
                    replyMsg.what = msg.what;
                    replyMsg.arg1 = msg.arg1;
                    Bundle data = new Bundle();
                    replyMsg.obj = data;
                    try {
                        replyTo.send(replyMsg);
                    } catch (RemoteException e) {
                        throw new RuntimeException(e);
                    }
                    break;
                }

                case Constants.EVENT_SETUP_WEBSOCKET: {
                    // 设置WebSocket服务器事件
                    final int what = msg.what;
                    final int arg1 = msg.arg1;

                    // 在指定端口启动WebSocket服务器
                    setupWebSocketServer(8080, object -> {
                        Message replyMsg = Message.obtain();
                        replyMsg.what = what;
                        replyMsg.arg1 = arg1;
                        Bundle data = new Bundle();
                        replyMsg.obj = data;

                        JSONObject result = (JSONObject) object;
                        data.putBoolean("success", result.names().length() > 0);
                        data.putString("result", result.toString());
                        try {
                            replyTo.send(replyMsg);
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                    });
                    break;
                }

                case Constants.EVENT_CONNECTED_DEVICE_INFO: {
                    final int what = msg.what;
                    final int arg1 = msg.arg1;
                    final String currentMac = (String) msg.obj;

                    getConnectedDeviceMacAddress(deviceList -> {
                        Message replyMsg = Message.obtain();
                        replyMsg.what = what;
                        replyMsg.arg1 = arg1;
                        Bundle data = new Bundle();
                        replyMsg.obj = data;

                        String connectedMac = getConnectedMacAddress(currentMac, deviceList);
                        data.putString("MAC", connectedMac);
                        try {
                            replyTo.send(replyMsg);
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                    });
                    break;
                }

                case Constants.EVENT_HTTP_REQUEST: {
                    String url = (String) msg.obj;

                    String result = null;
                    try {
                        result = httpRequest(new URL(url));
                    } catch (MalformedURLException e) {
                        e.printStackTrace();
                    }

                    Message replyMsg = Message.obtain();
                    replyMsg.what = msg.what;
                    replyMsg.arg1 = msg.arg1;
                    Bundle data = new Bundle();
                    replyMsg.obj = data;

                    data.putBoolean("success", result != null);
                    data.putString("result", result != null ? result : "");
                    try {
                        replyTo.send(replyMsg);
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                    break;
                }

                case Constants.EVENT_CAPTURE_IMAGE: {
                    Message replyMsg = Message.obtain();
                    replyMsg.what = msg.what;
                    replyMsg.arg1 = msg.arg1;
                    Bundle data = new Bundle();
                    replyMsg.obj = data;

                    String url = (String) msg.obj;
                    try {
                        long cur_time = System.currentTimeMillis();
                        Bitmap bitmap = captureImage(new URL(url));
                        cur_time = System.currentTimeMillis() - cur_time;

                        data.putBoolean("success", bitmap != null);
                        data.putLong("elapsed", cur_time);
                        data.putParcelable("bitmap", bitmap);
                        replyTo.send(replyMsg);
                    } catch (MalformedURLException | RemoteException e) {
                        e.printStackTrace();
                    }
                    break;
                }
            }
        }
    }

    /**
     * 服务创建回调
     * 初始化服务组件
     */
    @Override
    public void onCreate() {
        super.onCreate();

        Log.i(TAG, "onCreate called");

        mHandlerThread = new HandlerThread("SmartViewServiceThread", Process.THREAD_PRIORITY_BACKGROUND);
        mHandlerThread.start();
        mMessenger = new Messenger(new ServiceHandler(mHandlerThread.getLooper()));
    }

    /**
     * 绑定服务回调
     * 返回Messenger对象用于通信
     */
    @Override
    public IBinder onBind(Intent intent) {
        return mMessenger.getBinder();
    }

    /**
     * 服务启动命令回调
     * 支持粘性服务，在系统资源紧张被杀死后可以重启
     */
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        super.onStartCommand(intent, flags, startId);
        Log.i(TAG, "onStartCommand");
        return START_STICKY;
    }

    /**
     * 服务销毁回调
     * 清理资源
     */
    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.i(TAG, "onDestroy");

        mHandlerThread.quitSafely();
        WiFiApManager.getApManager(getApplicationContext()).destroy();

        if (webSocketServer != null) {
            try {
                webSocketServer.stop();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            webSocketServer = null;
            lastPingTimestamp = 0;
        }
    }

    /**
     * 启用WiFi接入点
     * 配置并启动WiFi热点
     */
    @RequiresPermission(allOf = {Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.NEARBY_WIFI_DEVICES})
    @RequiresApi(api = Build.VERSION_CODES.Q)
    private void setWifiApEnabled(boolean enabled, String hostSsid, WiFiApManager.OnSuccessListener onSuccessListener, WiFiApManager.OnFailureListener onFailureListener) {
        final WiFiApManager wifiApManager = WiFiApManager.getApManager(getApplicationContext());
        Log.i(TAG, "setWifiApEnabled called");
        if (enabled && wifiApManager.isWifiApEnabled()) {
            onSuccessListener.onSuccess(hostSsid, Constants.WIFI_AP_PASSWORD);
            return;
        }

        wifiApManager.setupWifiDirectAp(hostSsid, Constants.WIFI_AP_PASSWORD, (ssid, password) -> {
            Log.i(TAG, "Turn on hotspot successfully, ssid:" + ssid);
            onSuccessListener.onSuccess(ssid, password);
        }, (failureCode, e) -> {
            Log.i(TAG, "Failed to turn on hotspot, errorCode:" + failureCode);
            if (e != null) {
                e.printStackTrace();
            }
            onFailureListener.onFailure(failureCode, e);
        });
    }

    /**
     * 禁用WiFi接入点
     * 关闭WiFi热点
     */
    private void setWifiApDisabled() {
        Log.i(TAG, "setWifiApDisabled called");

        final WiFiApManager wifiApManager = WiFiApManager.getApManager(getApplicationContext());
        wifiApManager.disableWifiApHost();
    }

    // 蓝牙扫描状态标志
    private boolean mScanning = false;
    @RequiresPermission(Manifest.permission.BLUETOOTH_SCAN)
    private synchronized void scanBlueDevices(@NonNull AsyncResult asyncResult) {
        Log.i(TAG, "scanBlueDevices");

        if (mScanning) {
            return;
        }

        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED) {
            Log.e(TAG, "Error, bluetooth scan permission not permitted");
            return;
        }

        final BluetoothLeScanner scanner = BluetoothAdapter.getDefaultAdapter().getBluetoothLeScanner();
        final long startTime = SystemClock.elapsedRealtime();
        final ScanCallback callback = new ScanCallback() {
            private Set<String> mAddresses = new HashSet<>();

            @RequiresPermission(allOf = {Manifest.permission.BLUETOOTH_CONNECT, Manifest.permission.BLUETOOTH_SCAN})
            @Override
            public void onScanResult(int callbackType, @NonNull ScanResult result) {
                // Log.i(TAG, "onScanResult");

                if (mScanning && SystemClock.elapsedRealtime() - startTime >= 60 * 1000) {
                    Log.i(TAG, "timeout, stop scanning");
                    scanner.stopScan(this);
                    mScanning = false;
                    return;
                }

                BluetoothDevice device = result.getDevice();
                String name = device.getName();
                String address = device.getAddress();

                if (name == null || mAddresses.contains(address)) {
                    return;
                }

                mAddresses.add(address);
                Log.i(TAG, "Found device " + name + " (" + address + ")");

                if (name != null && name.startsWith(Constants.SMART_DEVICE_NAME_PREFIX)) {
                    if (asyncResult != null) {
                        asyncResult.onResult(StringUtils.getWiFiApSsid(name));
                    }

                    if (mScanning) {
                        Log.i(TAG, "Found one device, stop scanning...");
                        scanner.stopScan(this);
                        mScanning = false;
                    }
                }
            }

            public void onScanFailed(int errorCode) {
                super.onScanFailed(errorCode);
                Log.i(TAG, "onScanFailed");

                mScanning = false;
            }

            public void onBatchScanResults(List<ScanResult> results) {
                super.onBatchScanResults(results);

                Log.i(TAG, "onBatchScanResults");
            }
        };

        final ScanSettings settings = new ScanSettings
                .Builder()
                .build();

        final ScanFilter filter = new ScanFilter
                .Builder()
                .build();

        final List<ScanFilter> filters = new ArrayList<>();
        filters.add(filter);

        scanner.startScan(filters, settings, callback);
        mScanning = true;
    }

    /**
     * 获取已连接设备的MAC地址
     * 通过WiFi接入点管理器获取连接的设备信息
     */
    @RequiresPermission(allOf = {Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.NEARBY_WIFI_DEVICES})
    @RequiresApi(api = Build.VERSION_CODES.Q)
    private void getConnectedDeviceMacAddress(WiFiApManager.OnConnectedDeviceChangedListener listener) {
        Log.i(TAG, "getConnectedDeviceMacAddress");

        if (checkRemoteDisconnected()) {
            Log.i(TAG, "remote device disconnected");

            listener.onChange(new ArrayList<>());
            return;
        }

        WiFiApManager.getApManager(getApplicationContext()).requestConnectedDeviceMac(listener);
    }

    /**
     * 从设备列表中获取连接的MAC地址
     * 根据当前MAC和设备列表匹配合适的设备
     */
    private String getConnectedMacAddress(@NonNull String currentMac, @NonNull List<String> deviceList) {
        String connectedMac = "";

        if (!deviceList.isEmpty()) {
            if (currentMac.isEmpty()) {
                connectedMac = deviceList.get(0);
            } else {
                for (String mac : deviceList) {
                    if (mac.equalsIgnoreCase(currentMac)) {
                        connectedMac = mac;
                    }
                }
            }
        }

        return connectedMac;
    }

    /**
     * 发送HTTP请求并获取响应
     * @param url 请求URL
     * @return 响应内容字符串，失败返回null
     */
    @Nullable
    private String httpRequest(@NonNull URL url) {
        Log.i(TAG, "httpRequest: " + url);

        final OkHttpClient client = new OkHttpClient.Builder()
                .retryOnConnectionFailure(false)
                .build();

        try {
            final Request request = new Request.Builder().url(url).get().build();
            final Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                return response.body().string();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    /**
     * 设置WebSocket服务器
     * 创建并启动WebSocket服务
     */
    private void setupWebSocketServer(int port, @NonNull AsyncResult callback) {
        Log.d(TAG,"setupWebSocketServer called");
        if (webSocketServer != null) {
            try {
                webSocketServer.stop();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }

        try {
            webSocketServer = new SimpleWebSocketServer(port, callback);
            webSocketServer.setReuseAddr(true);
            webSocketServer.setTcpNoDelay(true);
            webSocketServer.start();
        } catch (UnknownHostException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 从URL捕获图像
     * 通过HTTP请求获取图像数据并转换为Bitmap
     */
    @Nullable
    private Bitmap captureImage(@NonNull URL url) {
        try {
            return BitmapFactory.decodeStream(url.openStream());
        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;
    }
}
