package com.microsoft.mai.smartview.stream;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.os.Message;
import android.os.Messenger;
import android.os.RemoteException;
import android.util.Log;

import androidx.annotation.NonNull;

import com.microsoft.mai.smartview.constant.Constants;
import com.microsoft.mai.smartview.gpt.GptBinaryStream;
import com.microsoft.mai.smartview.gpt.GptBinaryType;
import com.microsoft.mai.smartview.media.BitmapToJPEG;
import com.microsoft.mai.smartview.media.VideoExtractor;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URL;
import java.util.HashSet;
import java.util.Set;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 视频流处理类
 * 负责从设备接收视频流数据，发送到GPT服务，并处理返回结果
 * 支持实时视频流和已录制视频的处理
 * 继承自GptResponseAudio，可以处理音频响应
 */
public final class VideoStreamRunnable extends GptResponseAudio {
    private static final String TAG = "VideoStreamRunnable";
    // 停止视频流处理的标志
    private volatile boolean mStopVideoStream = false;

    // 用于向主线程发送消息的Messenger
    private final Messenger mMessenger;
    // 用于线程同步的倒计时锁
    private final CountDownLatch mCountDownLatch = new CountDownLatch(1);

    // 是否为视频模式（否则为图像模式）
    private final boolean mIsVideo;
    // 是否为持久模式
    private final boolean mPersistentMode;

    // 设备URL
    private final URL mDeviceURL;
    // GPT二进制流连接
    private final GptBinaryStream mGptBinaryStream;
    // 视频文件路径（如果处理本地视频）
    private final String mFilePath;
    // 时间戳数组，用于视频帧提取
    private final long[] mTimestampUs;

    /**
     * 构造函数
     * @param isVideo 是否为视频模式
     * @param persistentMode 是否为持久模式
     * @param messenger 消息发送器
     * @param deviceURL 设备URL
     * @param convId 会话ID
     * @param filePath 视频文件路径
     * @param timestampUs 时间戳数组
     */
    public VideoStreamRunnable(boolean isVideo,
                               boolean persistentMode,
                               @NonNull Messenger messenger,
                               @NonNull URL deviceURL,
                               @NonNull String convId,
                               @NonNull String filePath,
                               @NonNull long[] timestampUs) {
        super(false, Constants.RESPONSE_AUDIO_MP3);

        mIsVideo = isVideo;
        mPersistentMode = persistentMode;
        mMessenger = messenger;
        mDeviceURL = deviceURL;
        mFilePath = filePath;
        mTimestampUs = timestampUs;

        // 如果是非持久的视频模式且没有文件路径，设置默认视频持续时间的定时器
        if (mIsVideo && !mPersistentMode && mFilePath.isEmpty()) {
            final Timer timer = new Timer();
            timer.schedule(new TimerTask() {
                @Override
                public void run() {
                    stopVideoStream();
                }
            }, Constants.DEFAULT_VIDEO_DURATION * 1000L);
        }

        // 创建GPT二进制流连接
        mGptBinaryStream = new GptBinaryStream(GptBinaryType.BINARY_TYPE_IMAGE,
                mPersistentMode,
                Constants.URL_GPT_VIDEO_STREAM,
                convId,
                (action, params) -> {
                    switch (action) {
                        case GPT_ACTION_COMPLETE: {
                            // GPT处理完成
                            if (!mPersistentMode) {
                                final Message msg = Message.obtain();
                                msg.what = Constants.EVENT_GPT_PROCESSING;
                                try {
                                    messenger.send(msg);
                                } catch (RemoteException e) {
                                    e.printStackTrace();
                                }

                                stopVideoStream();
                            }
                            break;
                        }

                        case GPT_ACTION_ERROR: {
                            // GPT处理错误
                            stopVideoStream();

                            final Message msg = Message.obtain();
                            msg.what = Constants.EVENT_GPT_ERROR;
                            try {
                                messenger.send(msg);
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }
                            break;
                        }

                        case GPT_ACTION_UPDATE: {
                            // GPT返回更新内容
                            final Message msg = Message.obtain();
                            msg.what = Constants.EVENT_GPT_MESSAGE;
                            final Bundle bundle = new Bundle();
                            bundle.putString(Constants.KEY_GPT_RESPONSE_TEXT, params.toString());
                            msg.setData(bundle);
                            try {
                                messenger.send(msg);
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }
                            break;
                        }
                    }
                });
    }

    /**
     * 停止视频流处理
     * 关闭连接并释放资源
     */
    public void stopVideoStream() {
        mStopVideoStream = true;
        mCountDownLatch.countDown();
    }

    /**
     * 线程执行方法
     * 根据不同模式选择处理图像、视频或已录制视频
     */
    @Override
    public void run() {
        if (mStopVideoStream) {
            mGptBinaryStream.close();
            return;
        }

        if (!mIsVideo) {
            // 处理图像
            handleImage();
        } else if (!mFilePath.isEmpty()) {
            // 处理已录制视频
            handleRecordedVideo();
        } else {
            // 处理实时视频
            handleVideo();
        }
    }

    /**
     * 处理单张图像
     * 从设备获取图像数据并发送到GPT
     */
    private void handleImage() {
        final BlockingQueue<byte[]> queue = new LinkedBlockingQueue<>();
        DeviceStreamSubscriber.getInstance().addSubscriber(mDeviceURL, new DeviceStreamSubscriber.DeviceStreamDataListener() {
            @Override
            public void onDataReceived(@NonNull byte[] data) {
                try {
                    queue.put(data);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onError(@NonNull Throwable t) {
                Log.e(TAG, "onError: " + t.getMessage());
                notifyStreamingError();
            }
        });

        try {
            // 等待接收图像数据
            byte[] imageData = queue.poll(Constants.DEFAULT_IMAGE_TIMEOUT, TimeUnit.SECONDS);
            if (imageData != null) {
                // 将图像数据转换为位图并显示
                Bitmap bitmap = BitmapFactory.decodeByteArray(imageData, 0, imageData.length);
                showImage(bitmap, 0, true, true);

                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                BitmapToJPEG.convert(bitmap, outputStream);
                // 发送图像数据到GPT
                mGptBinaryStream.sendData(outputStream.toByteArray());
            } else {
                // 超时处理
                notifyStreamingError();
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
            notifyStreamingError();
        } finally {
            // 清理资源
            DeviceStreamSubscriber.getInstance().removeSubscriber(mDeviceURL, new DeviceStreamSubscriber.DeviceStreamDataListener() {
                @Override
                public void onDataReceived(@NonNull byte[] data) {}

                @Override
                public void onError(@NonNull Throwable t) {}
            });
        }
    }

    /**
     * 处理已录制的视频文件
     * 从文件中提取帧并发送到GPT
     */
    private void handleRecordedVideo() {
        try {
            // 获取要提取的时间点集合
            Set<Integer> timeSet = getTimeSetInSeconds(mTimestampUs, mPersistentMode);
            // 创建视频提取器
            VideoExtractor extractor = new VideoExtractor(mFilePath, timeSet, (bitmap, time) -> {
                try {
                    // 显示提取的帧
                    showImage(bitmap, time, false, true);

                    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                    BitmapToJPEG.convert(bitmap, outputStream);
                    // 发送帧数据到GPT
                    mGptBinaryStream.sendData(outputStream.toByteArray());
                } catch (IOException e) {
                    e.printStackTrace();
                }
            });

            // 开始提取视频帧
            extractor.start();
            // 等待处理完成或被中断
            mCountDownLatch.await();
        } catch (Exception e) {
            e.printStackTrace();
            notifyStreamingError();
        }
    }

    /**
     * 处理实时视频流
     * 从设备获取视频帧并发送到GPT
     */
    private void handleVideo() {
        final BlockingQueue<byte[]> queue = new LinkedBlockingQueue<>();
        // 添加视频流数据监听器
        DeviceStreamSubscriber.getInstance().addSubscriber(mDeviceURL, new DeviceStreamSubscriber.DeviceStreamDataListener() {
            @Override
            public void onDataReceived(@NonNull byte[] data) {
                try {
                    queue.put(data);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onError(@NonNull Throwable t) {
                Log.e(TAG, "onError: " + t.getMessage());
                notifyStreamingError();
            }
        });

        try {
            int second = 0;
            boolean firstFrame = true;
            long beginTimeMs = System.currentTimeMillis();

            while (!mStopVideoStream) {
                // 接收视频帧数据
                byte[] imageData = queue.poll(1, TimeUnit.SECONDS);
                if (imageData != null) {
                    // 计算当前时间
                    long curTimeMs = System.currentTimeMillis();
                    long diffTimeMs = curTimeMs - beginTimeMs;
                    int curSecond = (int) (diffTimeMs / 1000);

                    // 处理第一帧或者每秒一帧
                    if (firstFrame || curSecond > second) {
                        firstFrame = false;
                        second = curSecond;

                        // 解码图像数据
                        Bitmap bitmap = BitmapFactory.decodeByteArray(imageData, 0, imageData.length);
                        if (bitmap != null) {
                            // 显示图像
                            showImage(bitmap, diffTimeMs, false, firstFrame);

                            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                            BitmapToJPEG.convert(bitmap, outputStream);
                            byte[] jpegData = outputStream.toByteArray();
                            // 发送到GPT
                            mGptBinaryStream.sendData(jpegData);

                            // 优化内存使用
                            outputStream.close();
                            bitmap.recycle();
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            notifyStreamingError();
        } finally {
            // 移除监听器并清理资源
            DeviceStreamSubscriber.getInstance().removeSubscriber(mDeviceURL, new DeviceStreamSubscriber.DeviceStreamDataListener() {
                @Override
                public void onDataReceived(@NonNull byte[] data) {
                }

                @Override
                public void onError(@NonNull Throwable t) {
                }
            });

            // 通知GPT流传输结束
            mGptBinaryStream.finishSendData();
            // 重置UI
            resetImage();
        }
    }

    /**
     * 显示图像并发送消息更新UI
     * @param bitmap 要显示的位图
     * @param time 时间戳
     * @param updateText 是否更新文本
     * @param bubbleShow 是否显示气泡
     */
    private void showImage(Bitmap bitmap, long time, boolean updateText, boolean bubbleShow) {
        final Message msg = Message.obtain();
        msg.what = Constants.EVENT_RECEIVE_IMAGE;

        final Bundle bundle = new Bundle();
        bundle.putParcelable(Constants.KEY_BITMAP, bitmap);
        bundle.putLong(Constants.KEY_TIMESTAMP, time);
        bundle.putBoolean(Constants.KEY_UPDATE_TEXT, updateText);
        bundle.putBoolean(Constants.KEY_BUBBLE_SHOW, bubbleShow);
        msg.setData(bundle);

        try {
            mMessenger.send(msg);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 重置图像显示
     * 发送消息通知UI重置
     */
    private void resetImage() {
        final Message msg = Message.obtain();
        msg.what = Constants.EVENT_RESET_IMAGE;

        try {
            mMessenger.send(msg);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 通知流处理错误
     * 发送错误消息并停止处理
     */
    private void notifyStreamingError() {
        final Message msg = Message.obtain();
        msg.what = Constants.EVENT_STREAMING_ERROR;

        try {
            mMessenger.send(msg);
        } catch (RemoteException e) {
            e.printStackTrace();
        }

        stopVideoStream();
    }

    /**
     * 获取秒级时间点集合
     * 将微秒时间戳转换为秒级时间点
     * @param timeStampUs 微秒时间戳数组
     * @param isPersistentMode 是否为持久模式
     * @return 秒级时间点集合
     */
    @NonNull
    private static Set<Integer> getTimeSetInSeconds(@NonNull long[] timeStampUs, boolean isPersistentMode) {
        Set<Integer> timeSet = new HashSet<>();
        
        // 如果不是持久模式且有时间戳，使用提供的时间戳
        if (!isPersistentMode && timeStampUs.length > 0) {
            for (long us : timeStampUs) {
                timeSet.add((int) (us / 1000000));
            }
        } else {
            // 否则使用默认间隔
            for (int i = 0; i < Constants.DEFAULT_VIDEO_DURATION; i++) {
                if (i % Constants.DEFAULT_VIDEO_INTERVAL == 0) {
                    timeSet.add(i);
                }
            }
        }
        
        return timeSet;
    }
}
