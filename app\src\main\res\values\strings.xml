<resources>
    <string name="app_name">SmartView</string>
    <string name="service_name">SmartView Service</string>
    <string name="title_home">Smart View Copilot - GGEC</string>
    <string name="title_nearby">Nearby devices\u0020</string>

    <string name="none_found">No devices found</string>
    <string name="connected">Connected</string>
    <string name="disconnected">Disconnected</string>

    <string name="capture">Take a picture</string>
    <string name="captured_image">AI Camera</string>
    <string name="default_device">No Device Selected</string>
    <string name="device_ip">Device IP:</string>
    <string name="default_ip">0.0.0.0</string>
    <string name="device_mac">Device MAC:</string>
    <string name="default_mac">Unknown</string>
    <string name="frame_size">Frame Size:</string>
    <string name="frame_rate">Frame Rate:</string>
    <string name="battery_level">Battery Level:</string>
    <string name="default_battery_level">0%</string>
    <string name="rssi">Rssi:</string>
    <string name="default_rssi">-100 dB</string>
    <string name="firmware_version">Firmware Version:</string>
    <string name="default_firmware_version">0.0.0</string>
    <string name="app_version">APP Version:</string>
    <string name="default_fps">0 FPS</string>
    <string name="fps">FPS</string>
    <string name="start_preview">Record a video</string>
    <string name='stop_preview'>Stop recording</string>
    <string name="start_record_audio">Turn On Smart View Copilot</string>
    <string name="stop_record_audio">Turn Off Smart View Copilot</string>

    <string name="wifi_ap_success">Setup wifi ap host successfully</string>
    <string name="wifi_ap_failure">Failed to setup wifi ap host, please retry later</string>

    <string name="set_frame_size_success">Set frame size successfully</string>
    <string name="set_frame_size_failure">Failed to set frame size</string>
    <string name="status_failure">Failed to get status</string>

    <string name="turn_on_wifi_msg">Please turn on WiFi to continue</string>
    <string name="turn_on_wifi_btn_txt">Turn on WiFi</string>
    <string name="turn_on_wifi_exit">Exit</string>

    <string name="text_content_hint">Hey, what\'s on your mind?</string>
    <string name="device_not_found">Device not found</string>

    <string name="bluetooth_not_enabled">Please turn on Bluetooth</string>
    <string name="question">Question:\u0020</string>

    <string name="device_name">Device Name</string>

    <string name="me">Me</string>
    <string name="copilot">Copilot</string>

    <string name="mode_general">General Mode</string>
    <string name="mode_blind">Vision Support Mode</string>
    <string name="mode_elder">Elderly Mode</string>
    <string name="mode_travelling">Travelling Mode</string>
    <string name="mode_naturalist">Naturalist Mode</string>
    <string name="mode_translation">Translation Mode</string>
    <string name="mode_meeting">Meeting Mode</string>
    <string name="mode_health">Health Mode</string>

</resources>