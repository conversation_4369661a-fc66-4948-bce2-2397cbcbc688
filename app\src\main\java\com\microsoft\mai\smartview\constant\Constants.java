package com.microsoft.mai.smartview.constant;

/**
 * 常量类：定义应用中使用的各种常量值
 * 包括事件类型、网络参数、设备识别前缀和配置参数等
 */
public final class Constants {
    // 蓝牙扫描相关事件
    public final static int EVENT_BLE_SCAN = 1;
    // WiFi接入点设置事件
    public final static int EVENT_SETUP_WIFI_AP = 2;
    // WebSocket连接设置事件
    public final static int EVENT_SETUP_WEBSOCKET = 3;
    // 连接设备信息事件
    public final static int EVENT_CONNECTED_DEVICE_INFO = 4;
    // 停止WiFi接入点事件
    public final static int EVENT_STOP_WIFI_AP = 5;

    // HTTP请求事件
    public final static int EVENT_HTTP_REQUEST = 10;
    // 图像捕获事件
    public final static int EVENT_CAPTURE_IMAGE = 11;
    // 停止视频流事件
    public final static int EVENT_STOP_VIDEO_STREAMING = 12;
    // 流媒体错误事件
    public final static int EVENT_STREAMING_ERROR = 13;

    // GPT相关事件 - 拍照
    public final static int EVENT_GPT_TAKE_PHOTO = 20;
    // GPT相关事件 - 录制视频
    public final static int EVENT_GPT_TAKE_VIDEO = 21;
    // GPT相关事件 - 上传照片
    public final static int EVENT_GPT_UPLOAD_PHOTO = 22;
    // GPT相关事件 - 上传已捕获的照片
    public final static int EVENT_GPT_UPLOAD_CAPTURED_PHOTO = 23;
    // GPT相关事件 - 上传视频
    public final static int EVENT_GPT_UPLOAD_VIDEO = 24;
    // GPT相关事件 - 上传已录制的视频
    public final static int EVENT_GPT_UPLOAD_RECORDED_VIDEO = 25;
    // GPT相关事件 - 处理中
    public final static int EVENT_GPT_PROCESSING = 26;
    // GPT相关事件 - 流视频
    public final static int EVENT_GPT_STREAM_VIDEO = 27;
    // GPT相关事件 - 收到问答数据
    public final static int EVENT_GPT_RECEIVED_QA = 28;
    // GPT相关事件 - 问答查询
    public final static int EVENT_GPT_QA_QUERY = 29;
    // GPT相关事件 - 问答回答
    public final static int EVENT_GPT_QA_ANSWER = 30;
    // GPT相关事件 - 中断
    public final static int EVENT_GPT_DISRUPT = 31;
    // GPT相关事件 - 完成
    public final static int EVENT_GPT_COMPLETE = 32;
    // GPT相关事件 - 退出
    public final static int EVENT_GPT_EXIT = 33;
    // GPT相关事件 - 切换模式
    public final static int EVENT_GPT_SWITCH_MODE = 34;

    // GPT错误事件
    public final static int EVENT_GPT_ERROR = 100;

    // 智能设备名称前缀，用于设备识别
    public final static String SMART_DEVICE_NAME_PREFIX = "SmartSense-";
    // WiFi接入点SSID前缀
    public final static String WIFI_AP_SSID_PREFIX = "DIRECT-";

    // 心跳延迟时间（毫秒）
    public final static long HEARTBEAT_DELAY = 6000;

    // 默认视频时长（秒）
    public final static int DEFAULT_VIDEO_DURATION = 10;

    // GPT音频流WebSocket URL
    public final static String URL_GPT_AUDIO_STREAM = "wss://aihardware-demo.orangepebble-30028f23.southeastasia.azurecontainerapps.io/ws";
    //wss://classic.aihardware.carina.stca.microsoft.com/ws
    // GPT视频流WebSocket URL
    public final static String URL_GPT_VIDEO_STREAM = "wss://aihardware-demo.orangepebble-30028f23.southeastasia.azurecontainerapps.io/images";

    // URL Cookie存储，运行时可设置
    public static String URL_COOKIES = "";
    // GPT盐值，用于安全验证
    public static String GPT_SALT = "";
    // WiFi接入点密码，运行时可设置
    public static String WIFI_AP_PASSWORD = "";

    // 音频采样率（Hz）
    public final static int AUDIO_SAMPLE_RATE = 16000;
    // 是否使用MP3格式响应音频
    public final static boolean RESPONSE_AUDIO_MP3 = true;

    // Intent Extra键值 - SSID
    public final static String EXTRA_KEY_SSID = "EXTRA_KEY_SSID";
}
