package com.microsoft.mai.smartview.stream;

import android.media.AudioAttributes;
import android.media.AudioFormat;
import android.media.AudioTrack;

import com.microsoft.mai.smartview.constant.Constants;
import com.microsoft.mai.smartview.media.AudioDecoder;

/**
 * GPT响应音频处理类
 * 负责处理GPT返回的音频数据并播放
 * 支持MP3和PCM格式的音频处理
 */
public class GptResponseAudio implements Runnable {

    // 音频解码器，用于MP3解码
    private AudioDecoder mAudioDecoder;
    // 音频播放器，用于播放PCM数据
    private AudioTrack mAudioTrack;
    // 是否正在播放的标志
    private volatile boolean isPlaying = false;
    // 是否为MP3格式音频
    private boolean mAudioMp3;

    /**
     * 构造函数
     * @param createAudioTrack 是否创建AudioTrack实例
     * @param audioMp3 是否使用MP3格式音频
     */
    public GptResponseAudio(boolean createAudioTrack, boolean audioMp3) {
        mAudioMp3 = audioMp3;

        if (createAudioTrack) {
            // 创建流模式的AudioTrack
            createStreamModeAudioTrack();

            // 如果是MP3格式，创建并启动解码器
            if (mAudioMp3) {
                mAudioDecoder = new AudioDecoder(mAudioTrack);
                mAudioDecoder.startDecoding();
            }
        }
    }

    /**
     * 关闭音频播放器和解码器
     * 清理资源
     */
    public synchronized void close() {
        if (mAudioTrack != null) {
            // 如果是MP3格式，停止解码器
            if (mAudioMp3 && mAudioDecoder != null) {
                mAudioDecoder.stopDecoding();
            }

            // 停止并释放AudioTrack
            mAudioTrack.flush();
            mAudioTrack.stop();
            mAudioTrack.release();
            mAudioTrack = null;
            isPlaying = false;
        }
    }

    /**
     * 处理接收到的音频数据
     * 根据格式选择解码或直接播放
     * @param data 接收到的音频数据字节数组
     */
    public synchronized void doReceivedAudioData(byte[] data) {
        if (mAudioTrack == null || data == null || data.length == 0) {
            return;
        }

        // 如果尚未开始播放，启动AudioTrack
        if (!isPlaying) {
            try {
                mAudioTrack.play();
                isPlaying = true;
            } catch (IllegalStateException e) {
                e.printStackTrace();
            }
        }

        // 根据音频格式选择处理方式
        if (mAudioMp3 && mAudioDecoder != null) {
            // MP3格式：发送到解码器队列
            mAudioDecoder.queue(data);
        } else {
            // PCM格式：直接写入AudioTrack播放
            mAudioTrack.write(data, 0, data.length, AudioTrack.WRITE_BLOCKING);
        }
    }

    /**
     * 停止音频播放
     * 暂停AudioTrack并清空缓冲区
     */
    public synchronized void stopAudioPlaying() {
        if (mAudioTrack == null || !isPlaying) {
            return;
        }

        try {
            // 暂停播放并清空缓冲区
            mAudioTrack.pause();
            mAudioTrack.flush();
            isPlaying = false;
        } catch (IllegalStateException e) {
            e.printStackTrace();
        }
    }

    /**
     * 创建流模式的AudioTrack
     * 配置音频属性和格式
     */
    private void createStreamModeAudioTrack() {
        // 计算最小缓冲区大小
        final int bufferSize = AudioTrack.getMinBufferSize(
                Constants.AUDIO_SAMPLE_RATE,
                AudioFormat.CHANNEL_OUT_MONO,
                AudioFormat.ENCODING_PCM_16BIT);

        // 创建AudioTrack实例
        mAudioTrack = new AudioTrack.Builder()
                // 设置音频属性
                .setAudioAttributes(new AudioAttributes.Builder()
                        .setUsage(AudioAttributes.USAGE_MEDIA)
                        .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                        .build())
                // 设置音频格式
                .setAudioFormat(new AudioFormat.Builder()
                        .setEncoding(AudioFormat.ENCODING_PCM_16BIT)
                        .setSampleRate(Constants.AUDIO_SAMPLE_RATE)
                        .setChannelMask(AudioFormat.CHANNEL_OUT_MONO)
                        .build())
                // 设置缓冲区大小
                .setBufferSizeInBytes(bufferSize)
                // 设置传输模式为流模式
                .setTransferMode(AudioTrack.MODE_STREAM)
                .build();
    }

    /**
     * 线程执行方法
     * 在子类中实现具体逻辑
     */
    @Override
    public void run() {
        // 在子类中实现
    }
}
