<?xml version="1.0" encoding="utf-8"?>
<!-- 
    附近设备Fragment布局
    用于显示附近可连接的设备列表
    包含一个列表视图和一个空视图
-->
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.NearbyFragment"
    android:orientation="vertical">

    <!-- 附近设备列表视图 -->
    <ListView
        android:id="@+id/new_devices"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:stackFromBottom="true"
        android:layout_marginBottom="10dp"
        />

    <!-- 无设备时显示的空视图 -->
    <LinearLayout
        android:id="@+id/newEmptyView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:padding="10dp"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/none_found" />
    </LinearLayout>

</LinearLayout>