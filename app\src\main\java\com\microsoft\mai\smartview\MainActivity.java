package com.microsoft.mai.smartview;

import android.Manifest;
import android.app.AlertDialog;
import android.bluetooth.BluetoothAdapter;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.util.Log;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;
import androidx.navigation.ui.AppBarConfiguration;
import androidx.navigation.ui.NavigationUI;

import com.microsoft.mai.smartview.constant.Constants;
import com.microsoft.mai.smartview.databinding.ActivityMainBinding;

/**
 * 主活动类
 * 应用程序的主入口点，负责初始化UI、导航控制和权限请求
 * 管理WiFi连接状态，确保应用需要的WiFi和蓝牙功能可用
 */
public class MainActivity extends AppCompatActivity {
    private static final String TAG = "MainActivity";
    private static final int REQUEST_CODE_PERMISSIONS = 100;

    // WiFi管理器，用于控制WiFi状态
    private WifiManager mWifiManager;
    // 记录WiFi是否启用的标志
    private boolean isWifiEnabled = false;
    // 导航控制器，用于Fragment间导航
    private NavController mNavController;

    /**
     * 活动创建时调用
     * 初始化UI、导航和请求权限
     */
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mWifiManager = (WifiManager) getApplicationContext().getSystemService(Context.WIFI_SERVICE);

        // 使用视图绑定初始化界面
        ActivityMainBinding binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        setSupportActionBar(binding.toolbar);

        // 设置导航控制器和应用栏配置
        int[] fragmentIds = {R.id.navigation_home, R.id.navigation_nearby};
        AppBarConfiguration appBarConfiguration = new AppBarConfiguration.Builder(fragmentIds).build();
        mNavController = Navigation.findNavController(this, R.id.nav_host_fragment_activity_main);
        NavigationUI.setupActionBarWithNavController(this, mNavController, appBarConfiguration);

        // 根据Android版本请求所需权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED
                    || ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED
                    || ActivityCompat.checkSelfPermission(this, Manifest.permission.NEARBY_WIFI_DEVICES) != PackageManager.PERMISSION_GRANTED
                    || ActivityCompat.checkSelfPermission(this, Manifest.permission.CHANGE_WIFI_STATE) != PackageManager.PERMISSION_GRANTED
                    || ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_WIFI_STATE) != PackageManager.PERMISSION_GRANTED
                    || ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED
                    || ActivityCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {

                ActivityCompat.requestPermissions(this, new String[] {
                        Manifest.permission.BLUETOOTH_SCAN,
                        Manifest.permission.BLUETOOTH_CONNECT,
                        Manifest.permission.NEARBY_WIFI_DEVICES,
                        Manifest.permission.CHANGE_WIFI_STATE,
                        Manifest.permission.ACCESS_WIFI_STATE,
                        Manifest.permission.ACCESS_FINE_LOCATION,
                        Manifest.permission.WRITE_EXTERNAL_STORAGE,
                }, REQUEST_CODE_PERMISSIONS);
            }
        } else {
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.CHANGE_WIFI_STATE) != PackageManager.PERMISSION_GRANTED
                    || ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_WIFI_STATE) != PackageManager.PERMISSION_GRANTED
                    || ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED
                    || ActivityCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {

                ActivityCompat.requestPermissions(this, new String[]{
                        Manifest.permission.CHANGE_WIFI_STATE,
                        Manifest.permission.ACCESS_WIFI_STATE,
                        Manifest.permission.ACCESS_FINE_LOCATION,
                        Manifest.permission.WRITE_EXTERNAL_STORAGE,
                }, REQUEST_CODE_PERMISSIONS);
            }
        }
    }

    /**
     * 导航到主页Fragment
     * @param ssid WiFi网络的SSID
     */
    public void navigateToHome(@NonNull String ssid) {
        final Bundle data = new Bundle();
        data.putString(Constants.EXTRA_KEY_SSID, ssid);

        mNavController.popBackStack(R.id.navigation_nearby, true);
        mNavController.popBackStack(R.id.navigation_home, true);
        mNavController.navigate(R.id.navigation_home, data);
    }

    /**
     * 导航到附近设备Fragment
     */
    public void navigateToNearby() {
        mNavController.navigate(R.id.navigation_nearby, null);
    }

    /**
     * 处理权限请求结果
     * 记录每个请求的权限是否被授予
     */
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode != REQUEST_CODE_PERMISSIONS) {
            return;
        }

        for (int i = 0; i < permissions.length; i++) {
            if (grantResults[i] == PackageManager.PERMISSION_GRANTED) {
                Log.i(TAG, permissions[i] + " Granted");
            } else {
                Log.i(TAG, permissions[i] + " NOT Granted");
            }
        }
    }

    // 提示用户开启WiFi的对话框
    private AlertDialog mAlertDialog;

    /**
     * 活动恢复时调用
     * 检查WiFi状态，如果未启用则提示用户开启
     * 同时检查蓝牙状态并尝试启用
     */
    @Override
    protected void onResume() {
        super.onResume();

        isWifiEnabled = mWifiManager.isWifiEnabled();

        if (!isWifiEnabled) {
            // 尝试自动启用WiFi
            if (mWifiManager.setWifiEnabled(true)) {
                return;
            }

            // 避免重复显示对话框
            if (mAlertDialog != null && mAlertDialog.isShowing()) {
                return;
            }

            // 创建并显示提示开启WiFi的对话框
            final AlertDialog.Builder builder = new AlertDialog.Builder(this);
            builder.setMessage(R.string.turn_on_wifi_msg)
                    .setCancelable(false)
                    .setPositiveButton(R.string.turn_on_wifi_btn_txt, (dialog, id) -> {
                        Intent intent = new Intent(Settings.ACTION_WIFI_SETTINGS);
                        startActivity(intent);
                    })
                    .setNegativeButton(R.string.turn_on_wifi_exit, (dialog, id) -> {
                        dialog.cancel();
                        finish();
                    });

            mAlertDialog = builder.create();
            mAlertDialog.show();
        } else if (mAlertDialog != null && mAlertDialog.isShowing()) {
            mAlertDialog.cancel();
        }

        // 检查并尝试启用蓝牙
        if (!BluetoothAdapter.getDefaultAdapter().isEnabled()) {
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT)
                    != PackageManager.PERMISSION_GRANTED) {
                Toast.makeText(this, R.string.bluetooth_not_enabled, Toast.LENGTH_LONG).show();
                return;
            }

            BluetoothAdapter.getDefaultAdapter().enable();
        }
    }

    /**
     * 活动暂停时调用
     * 保存当前WiFi状态
     */
    @Override
    protected void onPause() {
        super.onPause();
        if (isWifiEnabled) {
            isWifiEnabled = mWifiManager.isWifiEnabled();
        }
    }
}