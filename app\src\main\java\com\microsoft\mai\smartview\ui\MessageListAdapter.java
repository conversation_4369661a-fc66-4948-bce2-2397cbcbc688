package com.microsoft.mai.smartview.ui;

import android.content.Context;
import android.graphics.Bitmap;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.microsoft.mai.smartview.R;

import java.util.ArrayList;
import java.util.List;

/**
 * 消息列表适配器
 * 用于在RecyclerView中显示聊天消息，支持文本和图片两种类型的消息
 * 实现了发送和接收两种不同样式的消息布局
 */
public class MessageListAdapter extends RecyclerView.Adapter {
    private static final String TAG = "MessageListAdapter";

    // 上下文对象
    private Context mContext;
    // 消息列表数据
    private List<UserMessage> mMessageList = new ArrayList<>();

    /**
     * 消息类型枚举
     * SENT: 发送的消息
     * RECEIVED: 接收的消息
     */
    public enum MessageType {
        SENT,
        RECEIVED
    }

    /**
     * 消息数据类
     * 包含消息内容、发送者信息、创建时间和图片数据
     */
    public static class Message {
        String message;
        User sender;
        long createdAt;
        Bitmap bitmap;
    }

    /**
     * 用户信息类
     * 包含用户昵称和头像资源ID
     */
    public static class User {
        String nickname;
        int profileResId;
    }

    /**
     * 用户消息类
     * 将消息内容和消息类型组合在一起
     */
    public static class UserMessage {
        private Message mMessage;
        private MessageType mMessageType;

        public UserMessage(@NonNull Message message, MessageType messageType) {
            this.mMessage = message;
            this.mMessageType = messageType;
        }

        public Message getMessage() {
            return mMessage;
        }

        public MessageType getMessageType() {
            return mMessageType;
        }
    }

    /**
     * 构造函数
     * @param context 上下文对象
     */
    public MessageListAdapter(Context context) {
        mContext = context;
    }

    /**
     * 清空所有消息
     */
    public void clearAll() {
        mMessageList.clear();
        notifyDataSetChanged();
    }

    /**
     * 添加新消息
     * @param userMessage 要添加的用户消息
     */
    public void addMessage(@NonNull UserMessage userMessage) {
        mMessageList.add(userMessage);
        notifyDataSetChanged();
    }

    /**
     * 获取消息总数
     * @return 消息列表的大小
     */
    @Override
    public int getItemCount() {
        return mMessageList.size();
    }

    /**
     * 获取指定位置的消息类型
     * @param position 消息位置
     * @return 消息类型的序号
     */
    @Override
    public int getItemViewType(int position) {
        return mMessageList.get(position).getMessageType().ordinal();
    }

    /**
     * 创建ViewHolder
     * 根据消息类型创建不同的ViewHolder
     */
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view;

        if (viewType == MessageType.SENT.ordinal()) {
            view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_message_sent, parent, false);
            return new SentMessageHolder(view);
        } else if (viewType == MessageType.RECEIVED.ordinal()) {
            view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_message_received, parent, false);
            return new ReceivedMessageHolder(view);
        }

        return null;
    }

    /**
     * 绑定ViewHolder
     * 根据消息类型绑定不同的数据
     */
    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, int position) {
        UserMessage message = mMessageList.get(position);

        if (holder.getItemViewType()== MessageType.SENT.ordinal()) {
            ((SentMessageHolder) holder).bind(message);
        } else if (holder.getItemViewType()== MessageType.RECEIVED.ordinal()) {
            ((ReceivedMessageHolder) holder).bind(message);
        }
    }

    /**
     * 发送消息的ViewHolder
     * 支持显示文本和图片
     */
    private class SentMessageHolder extends RecyclerView.ViewHolder {
        TextView messageText;
        ImageView imageView;
        SentMessageHolder(View itemView) {
            super(itemView);
            messageText = itemView.findViewById(R.id.text_message_body);
            imageView = itemView.findViewById(R.id.captured_image);
        }

        void bind(@NonNull UserMessage message) {
            String text = message.getMessage().message;
            Bitmap bitmap = message.getMessage().bitmap;

            if (text != null && !text.isEmpty()) {
                messageText.setText(text);

                imageView.setVisibility(View.GONE);
                messageText.setVisibility(View.VISIBLE);
            } else if (bitmap != null) {
                imageView.setImageBitmap(bitmap);

                messageText.setVisibility(View.GONE);
                imageView.setVisibility(View.VISIBLE);
            }
        }
    }

    /**
     * 接收消息的ViewHolder
     * 只支持显示文本
     */
    private class ReceivedMessageHolder extends RecyclerView.ViewHolder {
        TextView messageText;

        ReceivedMessageHolder(View itemView) {
            super(itemView);
            messageText = itemView.findViewById(R.id.text_message_body);
        }

        void bind(@NonNull UserMessage message) {
            messageText.setText(message.getMessage().message);
        }
    }
}

