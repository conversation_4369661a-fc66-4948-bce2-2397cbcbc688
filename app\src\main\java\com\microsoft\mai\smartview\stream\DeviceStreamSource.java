package com.microsoft.mai.smartview.stream;

import android.util.Log;

import androidx.annotation.NonNull;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * 设备流数据源类
 * 负责连接设备流媒体服务，获取多部分混合替换(multipart/x-mixed-replace)格式的流数据
 * 实现了Iterable接口，允许逐帧获取数据流
 */
public class DeviceStreamSource implements Iterable<byte[]> {
    private final static String TAG = "DeviceStreamSource";

    // 多部分混合替换内容类型
    private final static String MULTIPART_MIXED_REPLACE = "multipart/x-mixed-replace";
    // 边界标识符前缀
    private final static String BOUNDARY_PART = "boundary=";
    // 内容长度头部标识
    private final static String CONTENT_TYPE_HEADER = "content-length";

    // 设备流URL
    private final URL url;
    // 边界标识字符串
    private String boundaryPart;
    // HTTP连接对象
    private HttpURLConnection conn;
    // 帧数据迭代器
    private FramesIterator iterator;

    /**
     * 构造函数
     * @param url 设备流媒体服务的URL
     */
    public DeviceStreamSource(@NonNull URL url) {
        this.url = url;
    }

    /**
     * 连接到设备流媒体服务
     * 初始化HTTP连接并解析内容类型和边界信息
     * @throws IOException 连接异常时抛出
     */
    public void connect() throws IOException {
        conn = (HttpURLConnection) this.url.openConnection();

        conn.setConnectTimeout(5000);
        conn.setReadTimeout(3000);
        conn.connect();

        String contentType = conn.getContentType();
        if (contentType == null) {
            Log.d(TAG, "contentType is null, return.");
            return;
        }

        if (!contentType.startsWith(MULTIPART_MIXED_REPLACE)) {
            throw new IOException("Unsupported Content-Type: " + contentType);
        }

        boundaryPart = contentType.substring(contentType.indexOf(BOUNDARY_PART)
                + BOUNDARY_PART.length());

        Log.d(TAG, "contentType: " + contentType);
        Log.d(TAG, "boundaryPart: " + boundaryPart);
    }

    /**
     * 关闭连接并释放资源
     */
    public void close() {
        if (this.conn != null) {
            this.conn.disconnect();
        }

        if (this.iterator != null) {
            this.iterator.close();
        }
    }

    /**
     * 获取流数据的迭代器
     * @return 字节数组的迭代器，每次迭代返回一帧数据
     */
    @NonNull
    @Override
    public Iterator<byte[]> iterator() {
        try {
            if (this.iterator == null) {
                boolean isAudio = this.url.getPath().contains("audio");
                this.iterator = new FramesIterator(boundaryPart, conn, isAudio);
            }

            return this.iterator;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 帧数据迭代器内部类
     * 负责从流中解析每一帧数据
     */
    private static class FramesIterator implements Iterator<byte[]> {

        // 边界标识字符串
        private final String boundary;
        // 输入流
        private final InputStream stream;
        // 是否为音频流的标志
        private final boolean isAudio;
        // 是否还有更多数据的标志
        private volatile boolean hasNext;

        /**
         * 构造函数
         * @param boundaryPart 边界标识字符串
         * @param conn HTTP连接对象
         * @param isAudio 是否为音频流
         * @throws IOException 读取流异常时抛出
         */
        FramesIterator(@NonNull String boundaryPart, @NonNull HttpURLConnection conn, boolean isAudio) throws IOException {
            this.boundary = boundaryPart.startsWith("--") ? boundaryPart : "--" + boundaryPart;
            this.stream = new BufferedInputStream(conn.getInputStream(), 64 * 1024);
            this.isAudio = isAudio;
            this.hasNext = true;

            Log.d(TAG, "boundary: " + boundary);
        }

        /**
         * 从流中读取一行文本
         * @return 读取的文本行
         * @throws IOException 读取异常时抛出
         */
        @NonNull
        private String readLine() throws IOException {
            int capacity = 512;
            byte[] buffer = new byte[capacity];
            StringBuilder stringBuffer = new StringBuilder(512);
            for (;;) {
                stream.mark(capacity);
                int bytes = stream.read(buffer, 0, capacity);
                int i = 0;

                for (; i < bytes; i++) {
                    byte LF = 0x0A;
                    if (buffer[i] == LF) {
                        stream.reset();
                        stream.read(buffer, 0, i + 1);
                        stringBuffer.append(new String(buffer, 0, i));
                        return stringBuffer.toString().trim();
                    }
                }
                stringBuffer.append(new String(buffer, 0, bytes));
            }
        }

        /**
         * 读取数据直到遇到边界标识
         * @throws IOException 读取异常时抛出
         * @throws InterruptedException 线程中断时抛出
         */
        private void readUntilBoundary() throws IOException, InterruptedException {
            for(;;) {
                String s = readLine();
                if (boundary.equals(s) || !hasNext) {
                    break;
                } else if (s.equals(boundary + "--")) /* end of stream */{
                    hasNext = false;
                    break;
                }
            }
        }

        /**
         * 读取HTTP头部信息
         * @return 头部信息的键值对映射
         * @throws IOException 读取异常时抛出
         * @throws InterruptedException 线程中断时抛出
         */
        private Map<String, String> readHeaders() throws IOException, InterruptedException {
            Map<String, String> headers = new HashMap<>();
            for(;;) {
                String line = readLine();
                if (line.trim().isEmpty()) {
                    return headers;
                } else {
                    String[] parts = line.split(":");
                    if (parts.length != 2) {
                        Log.d(TAG, "line: " + line);
                    }
                    headers.put(parts[0].trim().toLowerCase(), parts[1].trim());
                }
            }
        }

        /**
         * 判断是否还有更多数据
         * @return 如果还有更多数据返回true，否则返回false
         */
        @Override
        public boolean hasNext() {
            return this.hasNext;
        }

        /**
         * 获取下一帧数据
         * @return 下一帧数据的字节数组
         */
        @Override
        public byte[] next() {
            byte[] buffer = new byte[0];
            try {
                readUntilBoundary();
                Map<String, String> headers = readHeaders();
                String contentLength = headers.get(CONTENT_TYPE_HEADER);
                int length;
                try {
                    length = Integer.parseInt(contentLength);
                } catch (NumberFormatException e) {
                    return buffer;
                }
                buffer = new byte[length];
                int bytes = 0;
                while (bytes < length) {
                    bytes += stream.read(buffer, bytes, length - bytes);
                }

                return buffer;
            } catch (IOException | InterruptedException e) {
                if (hasNext) {
                    throw new RuntimeException(e);
                }
            }

            return new byte[0];
        }

        /**
         * 移除操作（未实现）
         */
        @Override
        public void remove() {
            // do nothing
        }

        /**
         * 关闭迭代器和流
         */
        void close() {
            this.hasNext = false;
            try {
                this.stream.reset();
                this.stream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}