package com.microsoft.mai.smartview.gpt;

import android.os.SystemClock;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.microsoft.mai.smartview.constant.Constants;
import com.microsoft.mai.smartview.utils.MD5Utils;
import com.microsoft.mai.smartview.utils.RandomUtils;

import org.jetbrains.annotations.Contract;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.net.InetAddress;
import java.net.Socket;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import javax.net.SocketFactory;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.WebSocket;
import okhttp3.WebSocketListener;
import okio.ByteString;

/**
 * GPT二进制数据流实现类
 * 负责通过WebSocket与GPT服务建立连接并进行数据交换
 * 支持发送音频和图像二进制数据，并处理服务端返回的各类响应
 */
public class GptBinaryStream implements GptStream {
    private static final String TAG = "GptBinaryStream";
    // 创建固定大小为1的线程池，用于处理二进制数据回调
    private final static ExecutorService mThreadPool = Executors.newFixedThreadPool(1);

    // 二进制数据类型（音频或图像）
    private final GptBinaryType mGptBinaryType;
    // WebSocket服务器地址
    private final String mUrl;
    // GPT响应监听器，用于将服务端响应传递给调用者
    private final GptResponseListener mResponseListener;
    // 是否为持久模式（持久模式下WebSocket连接会保持较长时间）
    private final boolean mPersistentMode;
    // WebSocket连接实例
    private WebSocket mWebSocket;
    // 会话ID，用于标识当前交互会话
    private final String mConversationId;

    // 中断时间戳，用于跟踪和处理中断事件
    private long mDisruptTimestamp = 0L;

    /**
     * 构造函数
     * @param binaryType 二进制数据类型（音频或图像）
     * @param persistentMode 是否为持久模式
     * @param url WebSocket服务器地址
     * @param convId 会话ID
     * @param responseListener GPT响应监听器
     */
    public GptBinaryStream(GptBinaryType binaryType,
                           boolean persistentMode,
                           @NonNull String url,
                           @NonNull String convId,
                           @NonNull GptResponseListener responseListener) {
        this.mGptBinaryType = binaryType;
        this.mUrl = url;
        this.mResponseListener = responseListener;
        this.mPersistentMode = persistentMode;
        this.mConversationId = convId;

        // 初始化WebSocket连接
        initWebSockets();
    }

    /**
     * 发送二进制数据
     * @param data 二进制数据字节数组
     * @return 发送是否成功
     */
    public boolean send(byte[] data) {
        // 检查数据是否有效
        if (data == null || data.length == 0) {
            return false;
        }

        // 图像模式下，如果队列过大则取消连接以防止内存占用过多
        if (mGptBinaryType == GptBinaryType.BINARY_TYPE_IMAGE
                && mPersistentMode
                && mWebSocket.queueSize() >= data.length * 5L) {
            Log.e(TAG, "Image buffer is more than 5 images, clear all.");
            mWebSocket.cancel();
        }

        // 发送二进制数据
        boolean success = mWebSocket.send(ByteString.of(data));
        Log.i(TAG, "Queue size:" + mWebSocket.queueSize() + ", send byte[] success: " + success);

        return success;
    }

    /**
     * 发送文本数据
     * @param text 文本字符串
     * @return 发送是否成功
     */
    public boolean send(String text) {
        // 检查文本是否有效
        if (text == null || text.isEmpty()) {
            return false;
        }

        // 发送文本数据
        boolean success = mWebSocket.send(text);
        Log.i(TAG, "send text success: " + success);

        return success;
    }

    /**
     * 关闭WebSocket连接
     */
    public void close() {
        if (mWebSocket != null) {
            mWebSocket.close(1000, "bye");
        }
    }

    /**
     * 初始化WebSocket连接
     * 配置OkHttp客户端、添加请求头并设置监听器
     */
    private void initWebSockets() {
        Log.i(TAG, "initWebSockets");

        // 设置缓冲区大小为2MB
        final int bufferSize = 2 * 1024 * 1024;
        // 创建OkHttp客户端，配置连接超时和缓冲区大小
        final OkHttpClient httpClient = new OkHttpClient.Builder()
                .retryOnConnectionFailure(true)
                .writeTimeout(10, TimeUnit.SECONDS)
                .socketFactory(new RestrictedSocketFactory(bufferSize, bufferSize))
                .build();

        // 生成随机密钥用于安全验证
        final String key = RandomUtils.getRandomString();
        // 图像数据类型时使用会话ID，音频类型时不使用
        final String convSid = mGptBinaryType == GptBinaryType.BINARY_TYPE_IMAGE ? mConversationId : "";

        // 构建WebSocket请求，添加各种请求头信息
        Request request = new Request.Builder()
                .url(mUrl)
                .addHeader("key", key)
                // 添加MD5哈希值用于验证
                .addHeader("hash", MD5Utils.getMD5(key + Constants.GPT_SALT))
                .addHeader("conv_id", convSid)
                .addHeader("client", "SmartView-GGEC")
                // 设置音频响应格式
                .addHeader("audio_mp3", Constants.RESPONSE_AUDIO_MP3 ? "1" : "0")
                // 设置是否为持久模式
                .addHeader("persistent_mode", mPersistentMode ? "1" : "0")
                // 图像类型时设置Cookie
                .addHeader("Set-Cookie", mGptBinaryType == GptBinaryType.BINARY_TYPE_IMAGE ? Constants.URL_COOKIES : "")
                .build();

        // 创建WebSocket连接并设置监听器
        mWebSocket = httpClient.newWebSocket(request, new WebSocketListener() {
            /**
             * WebSocket连接关闭回调
             */
            @Override
            public void onClosed(@NonNull WebSocket webSocket, int code, @NonNull String reason) {
                super.onClosed(webSocket, code, reason);
                Log.i(TAG, "onClosed called");
            }

            /**
             * WebSocket连接失败回调
             * 处理超时和Socket异常，并通知调用者
             */
            @Override
            public void onFailure(@NonNull WebSocket webSocket, @NonNull Throwable t, @Nullable Response response) {
                super.onFailure(webSocket, t, response);
                Log.i(TAG, "onFailure called");

                // 处理Socket相关异常
                if (t instanceof SocketTimeoutException || t instanceof SocketException) {
                    t.printStackTrace();
                    // 通知调用者发生错误
                    mResponseListener.onResponse(GptAction.GPT_ACTION_ERROR, "");
                }
            }

            /**
             * 接收文本消息回调
             * 解析JSON数据并根据action_name确定具体操作
             */
            @Override
            public void onMessage(@NonNull WebSocket webSocket, @NonNull String text) {
                super.onMessage(webSocket, text);
                Log.i(TAG, "Received message from GPT server: " + text);

                try {
                    // 解析JSON数据
                    final JSONObject dataObject = new JSONObject(text).getJSONObject("data");
                    String action = dataObject.optString("action_name", "");
                    GptAction gptAction = GptAction.GPT_ACTION_UNKNOWN;

                    // 解析参数（可能为null）
                    Object params = null;
                    // 根据action_name确定对应的GptAction
                    switch (action) {
                        // 切换到通用模式
                        case "switch_mode_general":
                            gptAction = GptAction.GPT_ACTION_SWITCH_MODE_GENERAL;
                            break;

                        // 切换到盲人模式
                        case "switch_mode_blind":
                            gptAction = GptAction.GPT_ACTION_SWITCH_MODE_BLIND;
                            break;

                        // 切换到老年人模式
                        case "switch_mode_elder":
                            gptAction = GptAction.GPT_ACTION_SWITCH_MODE_ELDER;
                            break;

                        // 切换到旅行模式
                        case "switch_mode_traveling":
                            gptAction = GptAction.GPT_ACTION_SWITCH_MODE_TRAVELING;
                            break;

                        // 切换到自然观察模式
                        case "switch_mode_naturalist":
                            gptAction = GptAction.GPT_ACTION_SWITCH_MODE_NATURALIST;
                            break;

                        // 切换到翻译模式
                        case "switch_mode_translation":
                            gptAction = GptAction.GPT_ACTION_SWITCH_MODE_TRANSLATION;
                            break;

                        // 切换到会议模式
                        case "switch_mode_meeting":
                            gptAction = GptAction.GPT_ACTION_SWITCH_MODE_MEETING;
                            break;

                        // 切换到健康模式
                        case "switch_mode_health":
                            gptAction = GptAction.GPT_ACTION_SWITCH_MODE_HEALTH;
                            break;

                        // 拍照操作
                        case "take_photo":
                            gptAction = GptAction.GPT_ACTION_TAKE_PHOTO;
                            break;

                        // 录制视频操作，根据duration参数确定具体动作
                        case "take_video":
                            int duration = dataObject.getJSONObject("params").optInt("duration", -1);
                            if (duration > 0) {
                                gptAction = GptAction.GPT_ACTION_TAKE_VIDEO;
                                // 将视频时长作为参数传递
                                params = duration;
                            } else {
                                gptAction = GptAction.GPT_ACTION_VIDEO_START;
                            }
                            break;

                        // 开始录制视频
                        case "take_video_start":
                            gptAction = GptAction.GPT_ACTION_VIDEO_START;
                            break;

                        // 停止录制视频
                        case "take_video_stop":
                            gptAction = GptAction.GPT_ACTION_VIDEO_STOP;
                            break;

                        // 上传照片
                        case "upload_photo":
                            gptAction = GptAction.GPT_ACTION_UPLOAD_PHOTO;
                            break;

                        // 上传已捕获的照片
                        case "upload_captured_photo":
                            gptAction = GptAction.GPT_ACTION_UPLOAD_CAPTURED_PHOTO;
                            break;

                        // 上传视频，解析frames数组作为参数
                        case "upload_video":
                            gptAction = GptAction.GPT_ACTION_UPLOAD_VIDEO;
                            try {
                                // 解析frames数组，并转换为微秒时间戳
                                JSONArray jsonArray = dataObject.getJSONObject("params").getJSONArray("frames");
                                long[] longArray = new long[jsonArray.length()];
                                for (int i = 0; i < jsonArray.length(); i++) {
                                    longArray[i] = TimeUnit.SECONDS.toMicros(jsonArray.getLong(i));
                                }

                                params = longArray;
                            } catch (JSONException e) {
                                e.printStackTrace();
                            }
                            break;

                        // 上传已录制的视频，解析frames数组作为参数
                        case "upload_recorded_video":
                            gptAction = GptAction.GPT_ACTION_UPLOAD_RECORDED_VIDEO;

                            try {
                                // 解析frames数组，并转换为微秒时间戳
                                JSONArray jsonArray = dataObject.getJSONObject("params").getJSONArray("frames");
                                long[] longArray = new long[jsonArray.length()];
                                for (int i = 0; i < jsonArray.length(); i++) {
                                    longArray[i] = TimeUnit.SECONDS.toMicros(jsonArray.getLong(i));
                                }

                                params = longArray;
                            } catch (JSONException e) {
                                e.printStackTrace();
                            }
                            break;

                        // 问答文本回答，获取text参数
                        case "qa_text":
                            gptAction = GptAction.GPT_ACTION_QA_ANSWER;
                            params = dataObject.getJSONObject("params").optString("text", "");
                            break;

                        // 图像处理完成
                        case "images_complete":
                        // 问答处理完成
                        case "qa_complete":
                            gptAction = GptAction.GPT_ACTION_COMPLETE;
                            break;

                        // 用户查询，获取text参数
                        case "user_query":
                            gptAction = GptAction.GPT_ACTION_QA_QUERY;
                            params = dataObject.getJSONObject("params").optString("text", "");
                            break;

                        // 处理中状态
                        case "processing":
                            gptAction = GptAction.GPT_ACTION_PROCESSING;
                            break;

                        // 已连接状态，重置中断时间戳，获取会话ID
                        case "connected":
                            gptAction = GptAction.GPT_ACTION_CONNECTED;
                            mDisruptTimestamp = 0;
                            params = dataObject.getJSONObject("params").optString("conv_id", "");
                            break;

                        // 中断操作，设置中断时间戳，获取中断词
                        case "disrupt":
                            mDisruptTimestamp = SystemClock.elapsedRealtime();
                            gptAction = GptAction.GPT_ACTION_DISRUPT;
                            params = dataObject.getJSONObject("params").optString("disrupt_word", "");
                            break;

                        // 退出操作，重置中断时间戳
                        case "bye":
                            gptAction = GptAction.GPT_ACTION_EXIT;
                            mDisruptTimestamp = 0;
                            break;
                    }

                    Log.i(TAG, "GPT Action triggered: " + gptAction);
                    // 将解析后的操作和参数传递给监听器
                    mResponseListener.onResponse(gptAction, params);
                } catch (JSONException e) {
                    Log.e(TAG, "JSONException: " + e.getMessage());
                    e.printStackTrace();
                }
            }

            /**
             * 接收二进制数据回调
             * 主要处理音频数据响应
             */
            @Override
            public void onMessage(@NonNull WebSocket webSocket, @NonNull ByteString bytes) {
                super.onMessage(webSocket, bytes);
                Log.i(TAG, bytes.size() + " bytes data received");

                // 记录数据包接收时间戳
                final long packageTimestamp = SystemClock.elapsedRealtime();
                // 使用线程池处理二进制数据，避免阻塞主线程
                mThreadPool.submit(() -> {
                    // 如果收到的数据包时间戳早于中断时间戳，则忽略该数据包
                    if (mDisruptTimestamp > 0 && packageTimestamp <= mDisruptTimestamp) {
                        return;
                    }

                    // 将二进制数据传递给监听器
                    mResponseListener.onResponse(GptAction.GPT_ACTION_QA_BINARY, bytes.toByteArray());
                });
            }

            /**
             * WebSocket连接打开回调
             * 获取并保存Cookie信息
             */
            @Override
            public void onOpen(@NonNull WebSocket webSocket, @NonNull Response response) {
                super.onOpen(webSocket, response);
                // 音频数据类型时，保存服务端返回的Cookie
                if (mGptBinaryType == GptBinaryType.BINARY_TYPE_AUDIO) {
                    String cookies = response.headers().get("Set-Cookie");
                    if (cookies != null) {
                        Constants.URL_COOKIES = cookies;
                    }
                }

                Log.i(TAG, "onOpen called");
            }
        });
    }

    /**
     * 限制Socket缓冲区大小的SocketFactory实现
     * 用于控制WebSocket连接的缓冲区大小，优化内存使用
     */
    private static final class RestrictedSocketFactory extends SocketFactory {
        // 发送缓冲区大小
        private final int mSendBufferSize;
        // 接收缓冲区大小
        private final int mReadBufferSize;

        /**
         * 构造函数
         * @param sendBufferSize 发送缓冲区大小
         * @param readBufferSize 接收缓冲区大小
         */
        RestrictedSocketFactory(int sendBufferSize, int readBufferSize) {
            mSendBufferSize = sendBufferSize;
            mReadBufferSize = readBufferSize;
        }

        /**
         * 创建无连接的Socket
         */
        @NonNull
        @Contract(" -> new")
        @Override
        public Socket createSocket() throws IOException {
            return updateSendBufferSize(new Socket());
        }

        /**
         * 创建连接到指定主机和端口的Socket
         */
        @NonNull
        @Contract("_, _ -> new")
        @Override
        public Socket createSocket(String host, int port) throws IOException {
            return updateSendBufferSize(new Socket(host, port));
        }

        /**
         * 创建连接到指定主机和端口，并绑定到本地地址和端口的Socket
         */
        @NonNull
        @Contract("_, _, _, _ -> new")
        @Override
        public Socket createSocket(String host, int port, InetAddress localHost, int localPort) throws IOException {
            return updateSendBufferSize(new Socket(host, port, localHost, localPort));
        }

        /**
         * 创建连接到指定IP地址和端口的Socket
         */
        @NonNull
        @Contract("_, _ -> new")
        @Override
        public Socket createSocket(InetAddress host, int port) throws IOException {
            return updateSendBufferSize(new Socket(host, port));
        }

        /**
         * 创建连接到指定IP地址和端口，并绑定到本地地址和端口的Socket
         */
        @NonNull
        @Contract("_, _, _, _ -> new")
        @Override
        public Socket createSocket(InetAddress address, int port, InetAddress localAddress, int localPort) throws IOException {
            return updateSendBufferSize(new Socket(address, port, localAddress, localPort));
        }

        /**
         * 更新Socket的缓冲区大小和其他参数
         * @param socket 要更新的Socket
         * @return 更新后的Socket
         */
        @NonNull
        @Contract("_ -> param1")
        private Socket updateSendBufferSize(@NonNull Socket socket) throws IOException {
            // 设置发送缓冲区大小
            socket.setSendBufferSize(mSendBufferSize);
            // 设置接收缓冲区大小
            socket.setReceiveBufferSize(mReadBufferSize);
            // 启用TCP保活
            socket.setKeepAlive(true);
            // 禁用TCP的Nagle算法，提高实时性
            socket.setTcpNoDelay(true);
            // 设置超时时间为10秒
            socket.setSoTimeout(10000);
            return socket;
        }
    }
}
