package com.microsoft.mai.smartview.utils;

import android.os.Build;

/**
 * 硬件工具类
 * 提供设备硬件信息相关的功能，用于识别不同的硬件厂商
 */
public class HardwareUtils {

    /**
     * 检查是否为联发科硬件
     * 通过检查硬件标识符是否以"mt"开头来判断
     * @return 如果是联发科硬件则返回true
     */
    public static boolean isHardWareVendorMediaTek() {
        String hardware = Build.HARDWARE;
        if (hardware == null) {
            return false;
        }

        if (hardware.matches("mt[0-9]*")) {
            return true;
        }

        return false;
    }

    /**
     * 检查是否为Google Pixel设备
     * 通过检查设备型号是否以"Pixel"开头来判断
     * @return 如果是Google Pixel设备则返回true
     */
    public static boolean isHardWareGooglePixels() {
        String model = Build.MODEL;
        if (model == null) {
            return false;
        }

        if (model.startsWith("Pixel")) {
            return true;
        }

        return false;
    }

    /**
     * 检查是否为展讯硬件
     * 通过检查硬件标识符是否以"sp"开头或以"ums"开头来判断
     * @return 如果是展讯硬件则返回true
     */
    public static boolean isHardWareVendorSPRD() {
        String hardware = Build.HARDWARE;
        if (hardware == null) {
            return false;
        }

        if (hardware.matches("sp[0-9]*") || hardware.startsWith("ums")) {
            return true;
        }

        return false;
    }

    /**
     * 检查是否为高通硬件
     * 通过检查硬件标识符是否为"qcom"来判断
     * @return 如果是高通硬件则返回true
     */
    public static boolean isHardWareVendorQualcomm() {
        String hardware = Build.HARDWARE;
        if (hardware == null) {
            return false;
        }

        if (hardware.matches("qcom")) {
            return true;
        }

        return false;
    }
}
