package com.microsoft.mai.smartview.utils;

import androidx.annotation.NonNull;

import com.microsoft.mai.smartview.constant.Constants;

/**
 * 字符串工具类
 * 提供字符串处理相关的功能，包括：
 * 1. 按长度分割文本
 * 2. 字符串转十六进制
 * 3. 生成WiFi热点SSID
 */
public class StringUtils {
    /**
     * 按指定长度分割文本
     * @param text 要分割的文本
     * @param chunkLength 每个分段的长度
     * @return 分割后的字符串数组
     */
    @NonNull
    public static String[] splitTextByLength(@NonNull String text, int chunkLength) {
        if (chunkLength <= 0) {
            return new String[0];
        }

        int length = text.length();
        int arraySize = (int)Math.ceil(length * 1.0 / chunkLength);
        String[] result = new String[arraySize];

        for (int i = 0; i < arraySize; i++) {
            result[i] = text.substring(i * chunkLength, Math.min((i + 1) * chunkLength, length));
        }

        return result;
    }

    /**
     * 将字符串转换为十六进制字符串
     * @param input 要转换的输入字符串
     * @return 转换后的十六进制字符串
     */
    @NonNull
    public static String toHexString(@NonNull String input) {
        byte[] bytes = input.getBytes();
        StringBuilder builder = new StringBuilder();
        for (byte b : bytes) {
            builder.append(String.format("%02X", b));
        }

        return builder.toString();
    }

    /**
     * 根据设备名称生成WiFi热点SSID
     * @param deviceName 设备名称
     * @return 生成的WiFi热点SSID
     */
    @NonNull
    public static String getWiFiApSsid(@NonNull String deviceName) {
        String lastString = deviceName.substring(Constants.SMART_DEVICE_NAME_PREFIX.length());
        return Constants.WIFI_AP_SSID_PREFIX + lastString;
    }
}
