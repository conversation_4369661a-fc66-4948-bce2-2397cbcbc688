package com.microsoft.mai.smartview.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * MD5工具类
 * 提供MD5哈希计算功能
 */
public class MD5Utils {
    /**
     * 计算字符串的MD5哈希值
     * @param key 要计算MD5的字符串
     * @return 计算得到的MD5哈希值
     * @throws RuntimeException 如果MD5算法不可用
     */
    public static String getMD5(String key) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(key.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : messageDigest) {
                sb.append(String.format("%02x", b & 0xff));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5 algorithm not found", e);
        }
    }
}
