package com.microsoft.mai.smartview.stream;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.os.Message;
import android.os.Messenger;
import android.os.RemoteException;
import android.util.Log;

import androidx.annotation.NonNull;

import com.microsoft.mai.smartview.constant.Constants;
import com.microsoft.mai.smartview.media.AudioEncoder;
import com.microsoft.mai.smartview.media.VideoEncoder;
import com.microsoft.mai.smartview.utils.AudioUtils;
import com.microsoft.mai.smartview.utils.RandomUtils;

import org.jetbrains.annotations.Contract;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 视频流录制线程类
 * 负责从设备获取视频和音频流，并将其编码保存为媒体文件
 * 支持设置录制时长，录制完成后自动合并音视频
 */
public final class VideoStreamRecorderThread extends Thread {
    private final static String TAG = "VideoStreamRecorderThread";

    // 视频编码器，用于将视频流编码为视频文件
    private final VideoEncoder mVideoEncoder;
    // 音频编码器，用于将音频流编码为音频文件
    private final AudioEncoder mAudioEncoder;

    // 应用上下文
    private final Context mContext;
    // 视频流URL
    private final URL mVideoURL;
    // 音频流URL
    private final URL mAudioURL;

    // 视频文件
    private File mVideoFile;
    // 音频文件
    private File mAudioFile;
    // 合并后的目标文件
    private File mDestFile;

    // 停止视频流录制的标志
    private boolean mStopVideoStreaming = false;
    // 视频宽高
    private final int mWidth, mHeight;
    // 用于向主线程发送消息的Messenger
    private final Messenger mMessenger;

    /**
     * 构造函数
     * @param context 应用上下文
     * @param messenger 消息发送器
     * @param videoURL 视频流URL
     * @param audioURL 音频流URL
     * @param width 视频宽度
     * @param height 视频高度
     * @param duration 录制时长（秒），0表示不限时长
     */
    public VideoStreamRecorderThread(@NonNull Context context,
                                     @NonNull Messenger messenger,
                                     @NonNull URL videoURL,
                                     @NonNull URL audioURL,
                                     int width,
                                     int height,
                                     int duration) {
        mContext = context;
        mMessenger = messenger;
        mVideoURL = videoURL;
        mAudioURL = audioURL;

        mWidth = width;
        mHeight = height;

        // 计数器，用于跟踪视频和音频编码完成状态
        final AtomicInteger mRemainingCount = new AtomicInteger(2);
        
        // 初始化视频编码器并设置完成回调
        mVideoEncoder = new VideoEncoder(outputFile -> {
            Log.i(TAG, "Video created: " + outputFile.getPath());
            if (outputFile.length() == 0) {
                Log.e(TAG, "Create video failed.");

                outputFile.delete();
                return;
            }

            // 视频文件创建完成，检查是否可以合并音视频
            if (mRemainingCount.decrementAndGet() == 0) {
                combineAudioAndVideo();
            }
        });

        // 初始化音频编码器并设置完成回调
        mAudioEncoder = new AudioEncoder(outputFile -> {
            Log.i(TAG, "Audio created: " + outputFile.getPath() + ", file length: " + outputFile.length());
            if (outputFile.length() == 0) {
                Log.e(TAG, "Create audio failed.");

                outputFile.delete();
                return;
            }

            // 音频文件创建完成，检查是否可以合并音视频
            if (mRemainingCount.decrementAndGet() == 0) {
                combineAudioAndVideo();
            }
        });

        // 如果设置了录制时长，创建定时器在指定时间后停止录制
        if (duration > 0) {
            final Timer timer = new Timer();
            timer.schedule(new TimerTask() {
                @Override
                public void run() {
                    stopVideoStreaming();
                }
            }, duration * 1000L);
        }
    }

    /**
     * 停止视频流录制
     * 设置停止标志并通知编码器停止编码
     */
    public void stopVideoStreaming() {
        mStopVideoStreaming = true;
    }

    /**
     * 获取录制的视频文件路径
     * @return 视频文件路径
     */
    @NonNull
    public String getVideoFilePath() {
        return mDestFile != null ? mDestFile.getAbsolutePath() : "";
    }

    /**
     * 合并音频和视频文件
     * 使用AudioUtils工具类将音频和视频合并为一个MP4文件
     */
    private void combineAudioAndVideo() {
        try {
            mDestFile = AudioUtils.mixAudioAndVideo(mContext, mAudioFile, mVideoFile);
            Log.i(TAG, "Merge audio and video file: " + mDestFile.getPath());
            
            // 通知UI录制完成
            final Message msg = Message.obtain();
            msg.what = Constants.EVENT_RECORDING_COMPLETE;
            
            final Bundle bundle = new Bundle();
            bundle.putString(Constants.KEY_VIDEO_PATH, mDestFile.getPath());
            msg.setData(bundle);
            
            mMessenger.send(msg);
            
            // 通知系统媒体库扫描新文件
            mContext.sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(mDestFile)));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 初始化输出文件
     * 创建临时的音频和视频文件
     * @return 初始化成功返回true，否则返回false
     * @throws IOException 文件创建异常时抛出
     */
    private boolean initOutputFile() throws IOException {
        final File videoDir = mContext.getExternalFilesDir(Environment.DIRECTORY_MOVIES);
        if (videoDir == null) {
            return false;
        }
        
        mVideoFile = File.createTempFile(RandomUtils.getRandomString(8), ".tmp", videoDir);
        mAudioFile = File.createTempFile(RandomUtils.getRandomString(8), ".tmp", videoDir);
        return true;
    }

    /**
     * 线程运行方法
     * 初始化编码器并从设备获取视频和音频流进行录制
     */
    @Override
    public void run() {
        try {
            // 初始化输出文件
            if (!initOutputFile()) {
                return;
            }
            
            // 初始化编码器
            mVideoEncoder.initEncoder(mWidth, mHeight, mVideoFile.getPath());
            
            // 创建数据队列和视频流监听器
            final BlockingQueue<byte[]> queue = new LinkedBlockingQueue<>();
            final DeviceStreamSubscriber.DeviceStreamDataListener videoListener = new DeviceStreamSubscriber.DeviceStreamDataListener() {
                @Override
                public void onDataReceived(@NonNull byte[] data) {
                    try {
                        queue.put(data);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void onError(@NonNull Throwable t) {
                    Log.e(TAG, "Error for streaming: " + t.getMessage());
                    notifyStreamingError();
                }
            };

            // 开始录制音频
            final Thread audioThread = startAudioTrackRecord();
            audioThread.start();
            
            // 订阅视频流数据
            DeviceStreamSubscriber.getInstance().addSubscriber(mVideoURL, videoListener);
            
            // 通知UI开始录制
            toggleVideoRecording();
            
            // 视频帧计数和时间统计
            int frameIndex = 0;
            long totalTimeMs = 0;
            long startTimeMs = System.currentTimeMillis();
            
            // 主循环：获取视频帧并编码
            while (!mStopVideoStreaming) {
                // 获取视频帧数据
                byte[] data = queue.poll(3, TimeUnit.SECONDS);
                if (data == null) {
                    continue;
                }

                // 解码成位图
                Bitmap bitmap = BitmapFactory.decodeByteArray(data, 0, data.length);
                if (bitmap == null) {
                    continue;
                }

                // 计算时间并显示预览
                frameIndex++;
                long currentTimeMs = System.currentTimeMillis();
                long diffTimeMs = currentTimeMs - startTimeMs;
                totalTimeMs += diffTimeMs;
                
                if (frameIndex % 10 == 0) {
                    // 每10帧更新一次UI显示
                    showImage(bitmap, totalTimeMs / frameIndex, true);
                    totalTimeMs = 0;
                    frameIndex = 0;
                } else {
                    showImage(bitmap, 0, false);
                }

                // 编码视频帧
                mVideoEncoder.offerEncoder(bitmap, currentTimeMs);
                startTimeMs = currentTimeMs;
            }
            
            Log.i(TAG, "Stopping capturing");
            
            // 停止编码器
            mVideoEncoder.stopEncoder();
            mAudioEncoder.stopEncoder();
            
            // 取消订阅流数据
            DeviceStreamSubscriber.getInstance().removeSubscriber(mVideoURL, videoListener);
            
            // 重置UI
            resetImage();
            
            // 等待音频线程结束
            audioThread.join();
        } catch (Exception e) {
            e.printStackTrace();
            notifyStreamingError();
        }
    }

    /**
     * 显示图像并更新UI
     * @param bitmap 要显示的位图
     * @param time 时间戳
     * @param updateText 是否更新文本
     */
    private void showImage(Bitmap bitmap, long time, boolean updateText) {
        final Message msg = Message.obtain();
        msg.what = Constants.EVENT_RECEIVE_IMAGE;
        
        final Bundle bundle = new Bundle();
        bundle.putParcelable(Constants.KEY_BITMAP, bitmap);
        bundle.putLong(Constants.KEY_TIMESTAMP, time);
        bundle.putBoolean(Constants.KEY_UPDATE_TEXT, updateText);
        bundle.putBoolean(Constants.KEY_BUBBLE_SHOW, false);
        msg.setData(bundle);
        
        try {
            mMessenger.send(msg);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 重置图像显示
     * 发送消息通知UI重置
     */
    private void resetImage() {
        final Message msg = Message.obtain();
        msg.what = Constants.EVENT_RESET_IMAGE;
        
        try {
            mMessenger.send(msg);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 切换视频录制状态
     * 发送消息通知UI更新录制状态
     */
    private void toggleVideoRecording() {
        final Message msg = Message.obtain();
        msg.what = Constants.EVENT_TOGGLE_RECORDING;
        
        try {
            mMessenger.send(msg);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 通知流处理错误
     * 发送错误消息并停止录制
     */
    private void notifyStreamingError() {
        final Message msg = Message.obtain();
        msg.what = Constants.EVENT_STREAMING_ERROR;
        
        try {
            mMessenger.send(msg);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /**
     * 开始音频轨道录制
     * 创建并返回用于录制音频的线程
     * @return 音频录制线程
     */
    @NonNull
    @Contract(" -> new")
    private Thread startAudioTrackRecord() {
        return new Thread(() -> {
            // 初始化音频编码器
            mAudioEncoder.initEncoder(mAudioFile.getPath());
            
            // 创建数据队列和音频流监听器
            final BlockingQueue<byte[]> queue = new LinkedBlockingQueue<>();
            final DeviceStreamSubscriber.DeviceStreamDataListener audioListener = new DeviceStreamSubscriber.DeviceStreamDataListener() {
                @Override
                public void onDataReceived(@NonNull byte[] data) {
                    try {
                        queue.put(data);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void onError(@NonNull Throwable t) {
                    Log.e(TAG, "Error for streaming: " + t.getMessage());
                }
            };
            
            try {
                // 订阅音频流数据
                DeviceStreamSubscriber.getInstance().addSubscriber(mAudioURL, audioListener);
                
                // 主循环：获取音频数据并编码
                while (!mStopVideoStreaming) {
                    byte[] data = queue.poll(3, TimeUnit.SECONDS);
                    if (data == null) {
                        continue;
                    }
                    
                    // 编码音频数据
                    mAudioEncoder.offerEncoder(data);
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                // 取消订阅音频流
                DeviceStreamSubscriber.getInstance().removeSubscriber(mAudioURL, audioListener);
            }
        });
    }
}
