package com.microsoft.mai.smartview.media;

import android.graphics.Bitmap;
import android.media.MediaMetadataRetriever;
import android.util.Log;

import androidx.annotation.NonNull;

import java.io.IOException;

/**
 * 视频帧提取器类
 * 用于从视频文件中提取指定时间点的帧图像
 * 使用Android的MediaMetadataRetriever API实现
 */
public class VideoExtractor {
    private static final String TAG = "VideoExtractor";

    /**
     * 从视频文件中提取多个时间点的帧图像
     * 
     * @param videoFilePath 视频文件路径
     * @param timestampUs 要提取帧的时间点数组，单位为微秒
     * @return 提取的Bitmap图像数组，与时间点数组一一对应
     */
    @NonNull
    public static Bitmap[] extractFrames(@NonNull String videoFilePath, @NonNull long[] timestampUs) {
        if (timestampUs.length == 0) {
            return new Bitmap[0];
        }

        final Bitmap[] result = new Bitmap[timestampUs.length];
        try (MediaMetadataRetriever retriever = new MediaMetadataRetriever()) {
            // 设置视频文件源
            retriever.setDataSource(videoFilePath);

            /**
            // 获取视频总时长（以下代码已注释）
            String time = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
            long duration = Long.parseLong(time);
             */

            // 循环提取每个时间点的帧
            for (int i = 0; i < timestampUs.length; i++) {
                // 计算实际提取时间，当前直接使用指定时间
                long timeUs = /*duration * 1000L - */timestampUs[i];
                // 获取最接近指定时间的帧
                result[i] = retriever.getFrameAtTime(timeUs, MediaMetadataRetriever.OPTION_CLOSEST);
            }

            // 释放资源
            retriever.release();
        } catch (IOException e) {
            Log.e(TAG, "Error occurred: " + e.getMessage());
        }

        return result;
    }
}
