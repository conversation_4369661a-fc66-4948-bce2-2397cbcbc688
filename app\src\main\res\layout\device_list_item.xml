<?xml version="1.0" encoding="utf-8"?>
<!-- 
    设备列表项布局
    用于在附近设备列表中显示单个设备的信息
    包含设备名称、状态和MAC地址
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="10dp">

    <!-- 设备名称和状态容器 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">
        <!-- 设备名称文本 -->
        <TextView
            android:id="@+id/deviceName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="marquee"
            android:textSize="18sp"
            android:textStyle="bold"
            android:maxLines="1" />

        <!-- 设备状态文本 -->
        <TextView
            android:id="@+id/status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:layout_marginLeft="20dp"
            android:textStyle="bold"
            android:maxLines="1"/>
    </LinearLayout>

    <!-- 设备MAC地址文本 -->
    <TextView
        android:id="@+id/macAddress"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:maxLines="1"/>
</LinearLayout>
