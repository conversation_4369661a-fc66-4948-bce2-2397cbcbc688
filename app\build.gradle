plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace 'com.microsoft.mai.smartview'
    compileSdk 34
    project.archivesBaseName = "SmartView"//add by ggec luwanzhen,2024/8/15, modify the name of apk.

    defaultConfig {
        applicationId "com.microsoft.mai.smartview_ggec"
        minSdk 26
        // noinspection ExpiredTargetSdkVersion
        targetSdk 32
        versionCode 18
        versionName "1.5.0"

        multiDexEnabled true
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    buildFeatures {
        viewBinding true
    }
    //start,add by ggec luwanzhen,2024/8/15, modify the name of apk.
    android.applicationVariants.all { variant ->
        variant.outputs.all {
            def developStage = "beta"
            def createTime = new Date().format("YYYYMMdd", TimeZone.getTimeZone("GMT+08:00"))
            def fileName = "${project.archivesBaseName}-${defaultConfig.versionName}.${createTime}_${developStage}.apk"
            outputFileName = fileName
        }
    }
    //end, add by ggec luwanzhen
}

dependencies {

    implementation libs.appcompat
    implementation libs.material
    implementation libs.navigation.fragment
    implementation libs.navigation.ui
    implementation libs.okhttp
    implementation libs.websocket
    implementation libs.rxandroid
    implementation libs.multidex
    // implementation libs.ffmpeg.kit.full

}