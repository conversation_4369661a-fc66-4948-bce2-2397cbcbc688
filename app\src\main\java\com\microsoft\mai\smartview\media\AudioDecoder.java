package com.microsoft.mai.smartview.media;

import android.media.AudioFormat;
import android.media.AudioTrack;
import android.media.MediaCodec;
import android.media.MediaFormat;
import android.util.Log;

import androidx.annotation.NonNull;

import com.microsoft.mai.smartview.constant.Constants;
import com.microsoft.mai.smartview.utils.HardwareUtils;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CountDownLatch;

import io.reactivex.Completable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

/**
 * 音频解码器类
 * 负责将编码的音频数据(MP3格式)解码为PCM格式并播放
 * 使用Android MediaCodec进行硬件加速解码
 */
public class AudioDecoder {
    private static final String TAG = "AudioDecoder";
    // 超时时间，单位微秒
    private static final int TIMEOUT = 500000;

    // 音频采样率，从常量类获取
    private static final int SAMPLE_RATE = Constants.AUDIO_SAMPLE_RATE;
    // 通道数，单声道
    private static final int CHANNEL_COUNT = 1;
    // 比特率
    private static final int BIT_RATE = 32000;
    // 输入缓冲区大小 (512KB)
    private static final int INPUT_BUFFER = 512 * 1024;
    // 媒体类型，MP3音频
    private static final String MIME_TYPE = MediaFormat.MIMETYPE_AUDIO_MPEG;

    // 媒体解码器实例
    private MediaCodec mMediaCodec;

    // 解码队列，使用线程安全的并发队列
    private Queue<byte[]> mDecodeQueue = new ConcurrentLinkedQueue<>();
    // 标记是否没有更多数据包需要处理
    private volatile boolean mNoMorePackage = false;

    // 同步对象，用于等待新数据
    private final Object mSync = new Object();
    // 用于线程间等待新数据包的信号量
    private CountDownLatch mNewPackageLatch;

    // 音频播放器，用于播放解码后的PCM数据
    private AudioTrack mAudioTrack;
    // 解码器是否已初始化
    private volatile boolean mInited = false;

    /**
     * 构造函数
     * @param audioTrack 用于播放解码后音频的AudioTrack实例
     */
    public AudioDecoder(@NonNull AudioTrack audioTrack) {
        mAudioTrack = audioTrack;
    }

    /**
     * 开始解码过程
     * 初始化解码器并在后台线程开始解码操作
     */
    public void startDecoding() {
        if (mInited) {
            return;
        }

        // 针对特定硬件厂商(展讯)使用Google提供的MP3解码器
        if (HardwareUtils.isHardWareVendorSPRD()) {
            try {
                mMediaCodec = MediaCodec.createByCodecName("OMX.google.mp3.decoder");
            } catch(IllegalArgumentException | IOException e) {
                e.printStackTrace();
            }
        }

        // 如果上面的解码器创建失败，使用系统默认的解码器
        if (mMediaCodec == null) {
            try {
                mMediaCodec = MediaCodec.createDecoderByType(MIME_TYPE);
            } catch (IOException ex) {
                Log.e(TAG, "Error creating media codec decoder");
                return;
            }
        }

        // 创建并配置媒体格式
        MediaFormat mediaFormat = MediaFormat.createAudioFormat(MIME_TYPE, SAMPLE_RATE, CHANNEL_COUNT);
        mediaFormat.setInteger(MediaFormat.KEY_BIT_RATE, BIT_RATE);
        mediaFormat.setInteger(MediaFormat.KEY_CHANNEL_MASK, AudioFormat.CHANNEL_OUT_MONO);
        mediaFormat.setInteger(MediaFormat.KEY_MAX_INPUT_SIZE, INPUT_BUFFER);

        // 配置并启动解码器
        mMediaCodec.configure(mediaFormat, null, null, 0);
        mMediaCodec.start();

        // 在IO线程执行解码操作
        Completable.fromAction(this::decode)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe();

        mInited = true;
    }

    /**
     * 停止解码过程
     * 设置停止标志并通知等待线程
     */
    public void stopDecoding() {
        Log.i(TAG, "Stopping decoding");

        if (mMediaCodec == null) {
            Log.e(TAG, "Decoding not started");
            return;
        }

        // 设置停止标志
        mNoMorePackage = true;
        // 通知等待新数据的线程继续执行
        synchronized (mSync) {
            if ((mNewPackageLatch != null) && (mNewPackageLatch.getCount() > 0)) {
                mNewPackageLatch.countDown();
            }
        }
    }

    /**
     * 将音频数据添加到解码队列
     * @param audioData 要解码的音频数据字节数组
     */
    public void queue(@NonNull byte[] audioData) {
        Log.i(TAG, "Queueing audio bytes");

        if (mMediaCodec == null) {
            Log.e(TAG, "Decoding not started");
            return;
        }

        // 添加数据到队列
        mDecodeQueue.add(audioData);
        // 通知等待线程有新数据可用
        synchronized (mSync) {
            if ((mNewPackageLatch != null) && (mNewPackageLatch.getCount() > 0)) {
                mNewPackageLatch.countDown();
            }
        }
    }

    /**
     * 解码方法，在后台线程执行
     * 不断从队列获取数据，解码后通过AudioTrack播放
     */
    private void decode() {
        Log.i(TAG, "decode");

        if (mMediaCodec == null) {
            Log.e(TAG, "Decoding not started");
            return;
        }

        final MediaCodec.BufferInfo bufferInfo = new MediaCodec.BufferInfo();
        // 当还有数据包或队列不为空时继续解码
        while (!mNoMorePackage || !mDecodeQueue.isEmpty()) {
            // 从队列获取数据
            byte[] audioData = mDecodeQueue.poll();
            if (audioData == null) {
                // 如果没有数据，等待新数据到达
                synchronized (mSync) {
                    mNewPackageLatch = new CountDownLatch(1);
                }

                try {
                    mNewPackageLatch.await();
                } catch (InterruptedException ignore) {
                }

                audioData = mDecodeQueue.poll();
            }

            if (audioData == null) continue;

            try {
                // 获取输入缓冲区并填充数据
                int inputBufferIndex = mMediaCodec.dequeueInputBuffer(TIMEOUT);
                if (inputBufferIndex >= 0) {
                    ByteBuffer inputBuffer = mMediaCodec.getInputBuffer(inputBufferIndex);
                    inputBuffer.clear();
                    inputBuffer.limit(audioData.length);
                    inputBuffer.put(audioData);
                    mMediaCodec.queueInputBuffer(inputBufferIndex, 0, audioData.length, 0,
                            audioData.length == 0 ? MediaCodec.BUFFER_FLAG_END_OF_STREAM : 0);
                }

                // 获取并处理输出数据
                int waitTime = audioData.length == 0 ? -1 : 0;
                int outputBufferIndex = mMediaCodec.dequeueOutputBuffer(bufferInfo, waitTime);
                while(outputBufferIndex >= 0) {
                    if ((bufferInfo.flags & MediaCodec.BUFFER_FLAG_END_OF_STREAM) != 0) {
                        break;
                    }

                    // 获取解码后的音频数据
                    ByteBuffer outputBuffer = mMediaCodec.getOutputBuffer(outputBufferIndex);
                    outputBuffer.rewind();

                    byte []chunkAudio = new byte[bufferInfo.size];
                    outputBuffer.get(chunkAudio, 0, bufferInfo.size);

                    // 通过AudioTrack播放解码后的PCM数据
                    mAudioTrack.write(chunkAudio, 0, chunkAudio.length, AudioTrack.WRITE_BLOCKING);

                    // 释放输出缓冲区并获取下一个
                    mMediaCodec.releaseOutputBuffer(outputBufferIndex, false);
                    outputBufferIndex = mMediaCodec.dequeueOutputBuffer(bufferInfo, waitTime);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error occurred: " + e.getMessage());
                e.printStackTrace();
            }
        }

        // 解码完成，释放资源
        release();
    }

    /**
     * 释放解码器资源
     */
    public void release() {
        Log.i(TAG, "release");

        if (mMediaCodec != null) {
            try {
                mMediaCodec.stop();
                mMediaCodec.release();
            } catch (Exception e) {
                e.printStackTrace();
            }
            mMediaCodec = null;
            mInited = false;
        }
    }
}
