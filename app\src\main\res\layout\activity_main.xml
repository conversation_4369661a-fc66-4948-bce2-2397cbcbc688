<?xml version="1.0" encoding="utf-8"?>
<!-- 
    主活动布局
    垂直方向的线性布局，包含顶部工具栏和主导航容器
    整体页面结构由toolbar和导航宿主fragment组成
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- 顶部工具栏容器 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="?android:attr/actionBarSize"
        android:background="?attr/colorPrimary">

        <!-- 居中显示的工具栏 -->
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:textAppearance="?attr/textAppearanceTitleLarge"
            android:layout_marginLeft="0dp"
            android:layout_marginRight="10dp"
            app:titleTextColor="?attr/colorOnPrimary"
            app:navigationIcon="@drawable/ic_arrow_back" />
    </FrameLayout>

    <!-- 导航宿主Fragment，包含应用的主要内容 -->
    <fragment
        android:id="@+id/nav_host_fragment_activity_main"
        android:name="androidx.navigation.fragment.NavHostFragment"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:defaultNavHost="true"
        app:navGraph="@navigation/mobile_navigation" />

</LinearLayout>