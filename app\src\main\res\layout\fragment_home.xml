<?xml version="1.0" encoding="utf-8"?>
<!-- 
    主页Fragment布局
    展示摄像设备的主要信息和图像流
    包含设备控制、状态显示和详细信息区域
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:layout_marginBottom="20dp">

    <!-- 顶部图像显示区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:paddingTop="6dp"
        android:paddingBottom="6dp">

        <!-- 标题栏区域 -->
        <LinearLayout
            android:id="@+id/title_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/ic_rounded_corner"
            android:orientation="horizontal">

            <!-- 标题文本 -->
            <TextView
                android:layout_width="0dip"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:text="@string/captured_image"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:padding="10dp" />

            <!-- 计时器，用于录制时显示 -->
            <Chronometer
                android:id="@+id/chronometer"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:format="%s"
                android:padding="10dp"
                android:textColor="@color/md_theme_light_onPrimary"
                android:textSize="16sp"
                android:visibility="gone" />

            <!-- 设备设置按钮 -->
            <ImageButton
                android:id="@+id/imageButtonDeviceSettings"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="6dp"
                android:layout_gravity="center_vertical"
                android:src="@drawable/ic_device_settings" />

        </LinearLayout>

        <!-- 图像显示区域 -->
        <ImageView
            android:id="@+id/imageView"
            android:layout_width="match_parent"
            android:layout_height="248dp"
            android:background="@color/black"
            tools:srcCompat="@color/purple_500" />
    </LinearLayout>

    <!-- 设备标题信息栏 -->
    <RelativeLayout
        android:id="@+id/titleContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        android:background="@drawable/ic_rounded_corner">

        <!-- 设备名称显示 -->
        <TextView
            android:id="@+id/textViewDeviceName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="10dp"
            android:text="@string/default_device"
            android:textColor="@color/white"
            android:layout_alignParentLeft="true"
            android:textSize="16sp" />

        <!-- 连接状态图标 -->
        <ImageView
            android:id="@+id/imageViewConnectStatus"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginTop="4dp"
            android:layout_weight="1"
            android:layout_marginRight="6dp"
            android:layout_toLeftOf="@id/arrow"
            android:src="@drawable/ic_device_offline" />

        <!-- 箭头图标，用于展开/收起设备详细信息 -->
        <ImageView
            android:id="@+id/arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:paddingRight="10dp"
            android:src="@drawable/ic_arrow_down"
            android:layout_alignParentRight="true" />

    </RelativeLayout>

    <!-- 设备详细信息容器 -->
    <LinearLayout
        android:id="@+id/deviceInfoContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        android:paddingTop="4dp"
        android:paddingBottom="4dp"
        android:visibility="gone">

        <!-- IP地址信息行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingTop="2dp"
            android:paddingBottom="2dp">

            <TextView
                android:layout_width="0dip"
                android:layout_height="wrap_content"
                android:gravity="right|center_vertical"
                android:layout_weight="2"
                android:text="@string/device_ip" />

            <TextView
                android:id="@+id/textViewIPAddress"
                android:layout_width="0dip"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:layout_marginRight="10dp"
                android:text="@string/default_ip"
                android:paddingLeft="10dp"/>
        </LinearLayout>

        <!-- MAC地址信息行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingTop="2dp"
            android:paddingBottom="2dp">

            <TextView
                android:layout_width="0dip"
                android:layout_height="wrap_content"
                android:gravity="right|center_vertical"
                android:layout_weight="2"
                android:text="@string/device_mac" />

            <TextView
                android:id="@+id/textViewMACAddress"
                android:layout_width="0dip"
                android:layout_height="wrap_content"
                android:layout_marginRight="10dp"
                android:layout_weight="3"
                android:text="@string/default_mac"
                android:paddingLeft="10dp"/>
        </LinearLayout>

        <!-- 帧尺寸选择行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingTop="2dp"
            android:paddingBottom="2dp">

            <TextView
                android:layout_width="0dip"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:gravity="right|center_vertical"
                android:text="@string/frame_size" />

            <Spinner
                android:id="@+id/spinnerFrameSize"
                android:layout_width="0dip"
                android:layout_height="wrap_content"
                android:layout_marginRight="10dp"
                android:layout_weight="3"
                android:paddingLeft="2dp"/>
        </LinearLayout>

        <!-- 帧率信息行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingTop="2dp"
            android:paddingBottom="2dp">

            <TextView
                android:layout_width="0dip"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:gravity="right|center_vertical"
                android:text="@string/frame_rate"
                android:paddingLeft="10dp"/>

            <TextView
                android:id="@+id/textViewTime"
                android:layout_width="0dip"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:layout_marginRight="10dp"
                android:text="@string/default_fps"
                android:paddingLeft="10dp"/>
        </LinearLayout>

        <!-- 电池电量信息行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingTop="2dp"
            android:paddingBottom="2dp">

            <TextView
                android:layout_width="0dip"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:gravity="right|center_vertical"
                android:text="@string/battery_level" />

            <TextView
                android:id="@+id/textViewBattery"
                android:layout_width="0dip"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:layout_marginRight="10dp"
                android:paddingLeft="10dp"
                android:text="@string/default_battery_level"/>
        </LinearLayout>

        <!-- 信号强度信息行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingTop="2dp"
            android:paddingBottom="2dp">

            <TextView
                android:layout_width="0dip"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:gravity="right|center_vertical"
                android:text="@string/rssi" />

            <TextView
                android:id="@+id/textViewRssi"
                android:layout_width="0dip"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:layout_marginRight="10dp"
                android:paddingLeft="10dp"
                android:text="@string/default_rssi" />
        </LinearLayout>

        <!-- 固件版本信息行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingTop="2dp"
            android:paddingBottom="2dp">

            <TextView
                android:layout_width="0dip"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:gravity="right|center_vertical"
                android:text="@string/firmware_version" />

            <TextView
                android:id="@+id/textViewFirmware"
                android:layout_width="0dip"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:layout_marginRight="10dp"
                android:paddingLeft="10dp"
                android:text="@string/default_firmware_version" />
        </LinearLayout>
    </LinearLayout>

    <!-- 聊天消息列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recy_message_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:paddingBottom="10dp"
        android:clipToPadding="false"
        android:clipChildren="false" />

    <!-- 底部控制栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:paddingTop="6dp"
        android:paddingBottom="6dp">

        <!-- 显示/隐藏功能按钮行 -->
        <LinearLayout
            android:id="@+id/functionBtnsLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:visibility="gone">

            <!-- 拍照按钮 -->
            <Button
                android:id="@+id/btn_capture"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/photo"
                android:layout_margin="2dp"/>

            <!-- 录制按钮 -->
            <Button
                android:id="@+id/btn_record"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/record"
                android:layout_margin="2dp"/>

            <!-- GPT按钮 -->
            <Button
                android:id="@+id/btn_gpt"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/gpt"
                android:layout_margin="2dp"/>
        </LinearLayout>

        <!-- 输入区域 -->
        <LinearLayout
            android:id="@+id/layout_chatbox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingTop="2dp"
            android:background="?attr/colorSurface">

            <!-- 显示/隐藏功能按钮 -->
            <ImageButton
                android:id="@+id/button_function"
                android:layout_width="42dp"
                android:layout_height="42dp"
                android:background="?selectableItemBackgroundBorderless"
                android:padding="10dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_function" />

            <!-- 文本输入框 -->
            <EditText
                android:id="@+id/edittext_chatbox"
                android:hint="@string/input_hint"
                android:background="@drawable/input_border"
                android:layout_gravity="center"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:minHeight="40dp"
                android:layout_marginEnd="8dp"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:maxLines="1"
                android:textColorHint="?attr/colorOnSurfaceVariant"
                android:textColor="?attr/colorOnSurface"
                android:scrollHorizontally="true"
                android:padding="6dp"
                android:imeOptions="actionGo"/>

            <!-- 发送按钮 -->
            <ImageButton
                android:id="@+id/button_chatbox_send"
                android:layout_width="42dp"
                android:layout_height="42dp"
                android:background="?selectableItemBackgroundBorderless"
                android:padding="10dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_send" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>