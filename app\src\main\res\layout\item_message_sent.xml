<?xml version="1.0" encoding="utf-8"?>
<!-- 
    发送消息的列表项布局
    用于在聊天列表中显示发送的消息
    显示在屏幕右侧，带有特定的发送消息气泡样式
    支持显示文本消息和图片
-->
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginEnd="8dp"
    android:layout_marginTop="16dp">

    <!-- 消息卡片容器，使用透明背景 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/text_message_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:cardBackgroundColor="@android:color/transparent"
        app:cardCornerRadius="0dp"
        app:cardElevation="0dp"
        app:cardPreventCornerOverlap="false"
        app:cardUseCompatPadding="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 文本消息内容，使用发送消息气泡背景 -->
        <TextView
            android:id="@+id/text_message_body"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:background="@drawable/bubble_bkg_outgoing"
            android:maxWidth="320dp"
            android:paddingLeft="12dp"
            android:paddingTop="8dp"
            android:paddingRight="12dp"
            android:paddingBottom="12dp"
            android:textColor="?attr/colorOnPrimary"
            android:textSize="16sp" />

        <!-- 图片内容，使用相同的气泡背景 -->
        <ImageView
            android:id="@+id/captured_image"
            android:layout_width="260dp"
            android:layout_height="180dp"
            android:background="@drawable/bubble_bkg_outgoing"
            android:paddingLeft="8dp"
            android:paddingTop="8dp"
            android:paddingRight="12dp"
            android:paddingBottom="12dp" />
    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>