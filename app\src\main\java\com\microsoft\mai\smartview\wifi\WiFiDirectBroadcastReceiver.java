package com.microsoft.mai.smartview.wifi;

import android.Manifest;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.wifi.p2p.WifiP2pManager;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;

/**
 * WiFi Direct广播接收器
 * 用于接收和处理WiFi Direct相关的系统广播，包括：
 * 1. WiFi Direct状态变化
 * 2. 对等设备变化
 * 3. 连接状态变化
 * 4. 本机设备信息变化
 */
public class WiFiDirectBroadcastReceiver extends BroadcastReceiver {
    private static final String TAG = "WiFiDirectBroadcastReceiver";

    // WiFi热点管理器实例
    private final WiFiApManager mWifiApManager;

    /**
     * 构造函数
     * @param wifiApManager WiFi热点管理器实例
     */
    public WiFiDirectBroadcastReceiver(WiFiApManager wifiApManager) {
        super();
        this.mWifiApManager = wifiApManager;
    }

    /**
     * 接收广播
     * 处理不同类型的WiFi Direct广播
     */
    @Override
    public void onReceive(Context context, @NonNull Intent intent) {
        final String action = intent.getAction();

        // 处理WiFi Direct状态变化
        if (WifiP2pManager.WIFI_P2P_STATE_CHANGED_ACTION.equals(action)) {
            int state = intent.getIntExtra(WifiP2pManager.EXTRA_WIFI_STATE, -1);
            mWifiApManager.setIsWifiP2pEnabled(state == WifiP2pManager.WIFI_P2P_STATE_ENABLED);
        } 
        // 处理对等设备变化
        else if (WifiP2pManager.WIFI_P2P_PEERS_CHANGED_ACTION.equals(action)) {
            Log.i(TAG, "WIFI_P2P_PEERS_CHANGED_ACTION");
            if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED
                    || ActivityCompat.checkSelfPermission(context, Manifest.permission.NEARBY_WIFI_DEVICES) != PackageManager.PERMISSION_GRANTED) {
                return;
            }

            mWifiApManager.setRemotePeersChanged();
        } 
        // 处理连接状态变化
        else if (WifiP2pManager.WIFI_P2P_CONNECTION_CHANGED_ACTION.equals(action)) {
            // mApManager.requestConnectedDeviceIp(null);
        } 
        // 处理本机设备信息变化
        else if (WifiP2pManager.WIFI_P2P_THIS_DEVICE_CHANGED_ACTION.equals(action)) {
        }
    }
}