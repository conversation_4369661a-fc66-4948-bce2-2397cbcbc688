# SmartView系统架构概述

## 系统架构图

```mermaid
graph TB
    subgraph "Android客户端"
        A[MainActivity] --> B[HomeFragment]
        B --> C[SmartViewService]
        C --> D[AudioStreamRunnable]
        C --> E[VideoStreamRunnable]
        D --> F[GptBinaryStream]
        E --> F
        B --> G[MessageListAdapter]
        B --> H[UI状态管理]
    end
    
    subgraph "智能硬件设备"
        I[SmartSense设备] --> J[蓝牙广播]
        I --> K[WiFi热点连接]
        I --> L[音频采集]
        I --> M[视频采集]
        I --> N[WebSocket客户端]
    end
    
    subgraph "云端GPT服务"
        O[WebSocket服务器] --> P[音频流处理]
        O --> Q[图像流处理]
        P --> R[语音识别]
        Q --> S[图像分析]
        R --> T[GPT模型]
        S --> T
        T --> U[响应生成]
    end
    
    J --> C
    K --> C
    N --> F
    F --> O
    U --> F
```

## 核心组件说明

### 1. Android客户端层

#### 主要组件
- **MainActivity**: 应用主入口，管理Fragment生命周期
- **HomeFragment**: 核心UI控制器，处理用户交互和状态显示
- **SmartViewService**: 后台服务，管理设备连接和数据传输
- **AudioStreamRunnable**: 音频流处理线程
- **VideoStreamRunnable**: 视频流处理线程
- **GptBinaryStream**: WebSocket通信核心类

#### 关键功能
- 蓝牙设备扫描和连接
- WiFi热点建立和管理
- 音视频数据流处理
- AI交互界面展示
- 错误处理和状态管理

### 2. 智能硬件设备层

#### 设备功能
- **音频采集**: 16kHz采样率，16-bit精度，单声道
- **视频采集**: 可配置分辨率，JPEG格式输出
- **无线通信**: 蓝牙4.0+WiFi双模通信
- **本地处理**: 基础音视频预处理

#### 通信协议
- 蓝牙：设备发现和配对
- WiFi：高速数据传输
- WebSocket：实时双向通信

### 3. 云端GPT服务层

#### 服务架构
- **WebSocket服务器**: 处理客户端连接
- **音频处理引擎**: 语音识别和合成
- **图像处理引擎**: 计算机视觉分析
- **GPT模型**: 自然语言理解和生成
- **响应分发**: 多模态结果返回

#### AI能力
- 实时语音对话
- 图像内容理解
- 多模态交互
- 上下文记忆

## 数据流向图

```mermaid
sequenceDiagram
    participant U as 用户
    participant A as Android客户端
    participant D as 智能设备
    participant G as GPT服务
    
    U->>A: 启动应用
    A->>D: 蓝牙扫描
    D->>A: 广播设备信息
    A->>A: 建立WiFi热点
    D->>A: 连接WiFi热点
    A->>D: WebSocket握手
    
    U->>D: 语音输入
    D->>A: 音频数据流
    A->>G: WebSocket传输
    G->>G: 语音识别+AI处理
    G->>A: 文本+音频响应
    A->>U: 显示文本+播放音频
    
    U->>D: 拍照请求
    D->>A: 图像数据
    A->>G: 图像上传
    G->>G: 图像分析+AI处理
    G->>A: 分析结果
    A->>U: 显示分析结果
```

## 技术栈详解

### Android客户端技术栈
- **开发语言**: Java
- **UI框架**: Android原生View + ViewBinding
- **网络通信**: OkHttp3 WebSocket
- **音视频处理**: Android MediaCodec
- **异步处理**: RxJava2
- **数据存储**: SharedPreferences
- **线程管理**: ExecutorService + Handler

### 智能设备技术栈
- **硬件平台**: ESP32系列
- **操作系统**: FreeRTOS
- **通信协议**: Bluetooth LE + WiFi
- **音频编解码**: PCM/AAC
- **视频编解码**: JPEG
- **传输协议**: WebSocket over TCP/IP

### 云端服务技术栈
- **部署平台**: Azure Container Apps
- **WebSocket框架**: 自定义WebSocket服务器
- **AI模型**: GPT-4 / GPT-3.5-turbo
- **语音处理**: Azure Speech Services
- **图像处理**: Azure Computer Vision
- **负载均衡**: Azure Load Balancer

## 安全架构

### 1. 通信安全
- **传输加密**: WSS (WebSocket Secure)
- **身份验证**: 基于密钥的HMAC签名
- **会话管理**: 临时会话ID机制
- **数据完整性**: MD5校验和验证

### 2. 设备安全
- **设备认证**: MAC地址白名单
- **连接控制**: 一对一设备绑定
- **数据隔离**: 会话级数据隔离
- **权限控制**: 最小权限原则

### 3. 隐私保护
- **数据加密**: 端到端加密传输
- **临时存储**: 不持久化敏感数据
- **匿名化**: 去除个人标识信息
- **合规性**: 遵循GDPR等隐私法规

## 性能优化策略

### 1. 客户端优化
- **内存管理**: 及时释放Bitmap和MediaCodec资源
- **线程优化**: 高优先级线程处理音视频数据
- **缓存策略**: 合理的队列大小控制
- **电量优化**: 智能休眠和唤醒机制

### 2. 网络优化
- **连接复用**: WebSocket长连接
- **数据压缩**: 自适应质量压缩
- **流量控制**: 动态码率调整
- **重连机制**: 指数退避重连策略

### 3. 服务端优化
- **负载均衡**: 多实例部署
- **缓存机制**: Redis会话缓存
- **异步处理**: 非阻塞I/O模型
- **资源池**: 连接池和线程池管理

## 可扩展性设计

### 1. 模块化架构
- **插件化**: 支持功能模块动态加载
- **接口抽象**: 统一的AI服务接口
- **配置驱动**: 外部配置文件驱动
- **版本兼容**: 向后兼容的API设计

### 2. 多设备支持
- **设备抽象**: 统一的设备接口
- **协议适配**: 多种通信协议支持
- **能力发现**: 动态设备能力检测
- **负载分担**: 多设备协同工作

### 3. AI服务扩展
- **模型切换**: 支持多种AI模型
- **服务路由**: 智能服务选择
- **能力组合**: 多AI服务组合
- **自定义训练**: 支持模型微调

## 监控和运维

### 1. 系统监控
- **性能指标**: CPU、内存、网络使用率
- **业务指标**: 连接数、响应时间、成功率
- **错误监控**: 异常捕获和报警
- **日志分析**: 结构化日志收集

### 2. 故障处理
- **自动恢复**: 服务自愈机制
- **降级策略**: 服务降级和熔断
- **备份方案**: 多活部署
- **应急响应**: 快速故障定位和修复

### 3. 运维工具
- **部署自动化**: CI/CD流水线
- **配置管理**: 集中化配置管理
- **版本控制**: 灰度发布和回滚
- **监控面板**: 实时监控仪表板
