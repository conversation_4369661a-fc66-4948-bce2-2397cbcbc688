package com.microsoft.mai.smartview.ui;

// 导入必要的Android系统库
import android.annotation.SuppressLint; // 导入注解类，用于抑制特定的Lint警告
import android.app.Activity; // 导入活动类，表示应用程序的一个屏幕
import android.bluetooth.BluetoothAdapter; // 导入蓝牙适配器类，用于管理蓝牙功能
import android.content.ActivityNotFoundException; // 导入异常类，当尝试启动不存在的活动时抛出
import android.content.ComponentName; // 导入组件名称类，用于标识服务组件
import android.content.Context; // 导入上下文类，提供应用环境信息
import android.content.Intent; // 导入意图类，用于启动活动和服务
import android.content.ServiceConnection; // 导入服务连接接口，用于与服务通信
import android.content.SharedPreferences; // 导入共享首选项类，用于存储简单的键值对
import android.content.pm.PackageInfo; // 导入包信息类，包含应用程序包的信息
import android.content.pm.PackageManager; // 导入包管理器类，用于检索包信息
import android.graphics.Bitmap; // 导入位图类，用于处理图像
import android.media.MediaActionSound; // 导入媒体动作声音类，用于播放系统声音
import android.media.MediaPlayer; // 导入媒体播放器类，用于播放音频
import android.net.wifi.WifiManager; // 导入WiFi管理器类，用于管理WiFi连接
import android.os.Bundle; // 导入Bundle类，用于传递数据
import android.os.Handler; // 导入处理器类，用于在线程间发送和处理消息
import android.os.IBinder; // 导入绑定接口，用于与服务通信
import android.os.Message; // 导入消息类，用于线程间通信
import android.os.Messenger; // 导入信使类，用于跨进程通信
import android.os.RemoteException; // 导入远程异常类，当跨进程通信失败时抛出
import android.os.SystemClock; // 导入系统时钟类，用于获取系统启动以来的时间
import android.provider.MediaStore; // 导入媒体存储类，用于访问媒体文件
import android.util.Log; // 导入日志类，用于记录调试信息
import android.view.LayoutInflater; // 导入布局填充器类，用于加载XML布局
import android.view.View; // 导入视图类，UI组件的基类
import android.view.ViewGroup; // 导入视图组类，容纳其他视图的容器
import android.widget.AdapterView; // 导入适配器视图类，用于显示列表数据
import android.widget.ArrayAdapter; // 导入数组适配器类，用于绑定数据到列表
import android.widget.Toast; // 导入吐司类，用于显示短暂的消息

import androidx.annotation.NonNull; // 导入非空注解类，用于标记不能为空的参数
import androidx.appcompat.app.AppCompatActivity; // 导入兼容性活动类，提供向后兼容的功能
import androidx.fragment.app.Fragment; // 导入片段类，UI的可重用部分
import androidx.recyclerview.widget.LinearLayoutManager; // 导入线性布局管理器类，用于RecyclerView

import com.microsoft.mai.smartview.MainActivity; // 导入主活动类
import com.microsoft.mai.smartview.R; // 导入资源类，用于访问应用资源
import com.microsoft.mai.smartview.SmartViewApplication; // 导入应用程序类，提供全局应用状态
import com.microsoft.mai.smartview.constant.Constants; // 导入常量类，包含应用中使用的常量
import com.microsoft.mai.smartview.constant.UserMode; // 导入用户模式枚举，定义不同的用户模式
import com.microsoft.mai.smartview.databinding.FragmentHomeBinding; // 导入主页片段绑定类，用于视图绑定
import com.microsoft.mai.smartview.media.BitmapToJPEG; // 导入位图转JPEG类，用于图像转换
import com.microsoft.mai.smartview.service.SmartViewService; // 导入智能视图服务类，提供后台服务
import com.microsoft.mai.smartview.stream.AudioStreamRunnable; // 导入音频流运行类，处理音频流
import com.microsoft.mai.smartview.stream.DeviceStreamSubscriber; // 导入设备流订阅者类，管理流订阅
import com.microsoft.mai.smartview.stream.VideoStreamRecorderThread; // 导入视频流记录线程类，用于记录视频
import com.microsoft.mai.smartview.stream.VideoStreamRunnable; // 导入视频流运行类，处理视频流

import org.json.JSONException; // 导入JSON异常类，处理JSON解析错误
import org.json.JSONObject; // 导入JSON对象类，用于处理JSON数据

import java.net.MalformedURLException; // 导入URL格式异常类，当URL格式不正确时抛出
import java.net.URL; // 导入URL类，表示统一资源定位符
import java.util.HashMap; // 导入哈希映射类，用于存储键值对
import java.util.Locale; // 导入区域设置类，用于国际化
import java.util.Map; // 导入映射接口，定义键值对集合
import java.util.Timer; // 导入计时器类，用于安排任务在将来执行
import java.util.TimerTask; // 导入计时器任务类，用于定义计时器执行的任务
import java.util.concurrent.ConcurrentHashMap; // 导入并发哈希映射类，线程安全的哈希映射
import java.util.concurrent.ExecutorService; // 导入执行器服务接口，用于管理线程
import java.util.concurrent.Executors; // 导入执行器类，提供线程池实现

/**
 * 主页片段类
 * 负责显示和控制智能设备的主界面，包括视频流、音频流、设备状态等功能
 * 通过WiFi和WebSocket与智能设备通信，实现远程控制和数据传输
 */
public class HomeFragment extends Fragment {
    private static final String TAG = "HomeFragment"; // 日志标签，用于调试和日志记录
    private static final int LowBatteryLevel = 15; // 低电量阈值，当设备电量低于此值时发出提醒

    private static SharedPreferences sp = null; // 共享首选项，用于存储应用设置和状态

    private WifiManager mWifiManager = null; // WiFi管理器，用于管理WiFi连接
    private FragmentHomeBinding mBinding; // 视图绑定对象，用于访问布局中的视图元素

    private boolean mVideoStreaming = false; // 视频流状态标志，表示是否正在进行视频流传输
    private boolean mAudioStreaming = false; // 音频流状态标志，表示是否正在进行音频流传输
    private int mCurrentFrameSizeIndex = 0; // 当前帧大小索引，用于设置视频分辨率
    private boolean mAIButtonPlaySound = true; // AI按钮播放声音标志，控制是否播放提示音

    private Timer mCheckHeartBeatTimer = null; // 心跳检测计时器，用于检测设备连接状态

    private AudioStreamRunnable mAudioStreamRunnable = null; // 音频流运行对象，处理音频流数据
    private VideoStreamRunnable mVideoStreamRunnable = null; // 视频流运行对象，处理视频流数据
    private VideoStreamRecorderThread mVideoStreamRecorderThread = null; // 视频流记录线程，用于录制视频

    private String mRemoteIpAddress = ""; // 远程设备IP地址
    private String mRemoteMacAddress = ""; // 远程设备MAC地址

    private boolean mArrowExpanded = false; // 箭头展开状态标志，控制设备信息面板的显示
    private boolean mConnected = false; // 连接状态标志，表示是否已连接到设备
    private long mLowBatteryNotifiedTimestamp = 0; // 低电量通知时间戳，用于控制通知频率
    private long mTouchTimestamp = 0; // 触摸时间戳，用于检测用户交互

    private MessageListAdapter mMessageAdapter; // 消息列表适配器，用于显示对话消息

    private String mCapturedImagePath = ""; // 捕获图像的路径，存储最近拍摄的图片位置
    private final Map<Integer, MediaPlayer> mMediaPlayers = new ConcurrentHashMap<>(); // 媒体播放器映射，用于管理多个音频播放

    // 线程池，用于执行后台任务，设置为高优先级
    private final static ExecutorService mThreadPool = Executors.newFixedThreadPool(4, r -> {
        Thread t = new Thread(r);
        t.setPriority(Thread.MAX_PRIORITY); // 设置线程优先级为最高
        return t;
    });

    @SuppressWarnings("SpellCheckingInspection")
    private final static String[] FrameSizeItems = new String[] { // 帧大小选项数组，用于视频分辨率选择
            "96X96",
            "QQVGA_160x120",
            "QCIF_176x144",
            "HQVGA_240x176",
            "240X240",
            "QVGA_240x240",
            "CIF_400x296",
            "HVGA_480x320",
            "VGA_640x480",
            "SVGA_800x600",
            "XGA_1024x768",
            "HD_1280x720",
            "SXGA_1280x1024",
            "UXGA_1600x1200",
    };

    private final static Map<Integer, int[]> FrameSizeMaps = new HashMap<Integer, int[]>() {{ // 帧大小映射，将索引映射到实际分辨率
        put(0, new int[] {96, 96});
        put(1, new int[] {160, 120});
        put(2, new int[] {176, 144});
        put(3, new int[] {240, 176});
        put(4, new int[] {240, 240});
        put(5, new int[] {240, 240});
        put(6, new int[] {400, 296});
        put(7, new int[] {480, 320});
        put(8, new int[] {640, 480});
        put(9, new int[] {800, 600});
        put(10, new int[] {1024, 768});
        put(11, new int[] {1280, 720});
        put(12, new int[] {1280, 1024});
        put(13, new int[] {1600, 1200});
    }};

    private enum RequestType { // 请求类型枚举，用于区分不同的HTTP请求
        TYPE_NORMAL, // 普通请求
        TYPE_FRAME_SIZE, // 帧大小设置请求
        TYPE_STATUS, // 状态查询请求
    }

    private boolean isServiceBound = false; // 服务绑定状态标志，表示是否已绑定到SmartViewService
    private final Messenger mActivityMessenger = new Messenger(new UiEventHandler()); // 活动信使对象，用于接收服务发送的消息
    private Messenger mMessenger; // 服务信使对象，用于向服务发送消息
    private String mCurrentDeviceSsid; // 当前设备的SSID，用于WiFi连接

    private UserMode mCurrrentUserMode = UserMode.MODE_GENERAL; // 当前用户模式，默认为通用模式

    /**
     * UI事件处理器类
     * 处理从服务接收到的消息，更新UI和状态
     * 作为Messenger的消息处理器，实现进程间通信
     */
    private final class UiEventHandler extends Handler {

        /**
         * 构造函数
         * 初始化Handler，使用默认的Looper
         */
        public UiEventHandler() {
            super();
        }

        /**
         * 处理接收到的消息
         * 根据消息类型执行不同的操作，如更新UI、处理设备状态等
         * @param msg 接收到的消息对象
         */
        @Override
        public void handleMessage(@NonNull Message msg) {
            Log.i(TAG, "handleMessage: " + msg); // 记录接收到的消息
            final Bundle data = (Bundle) msg.obj; // 获取消息中的数据包

            switch (msg.what) { // 根据消息类型执行不同的操作
                case Constants.EVENT_BLE_SCAN: { // 蓝牙扫描事件
                    String ssid = data.getString("ssid"); // 获取扫描到的SSID
                    if (ssid != null) {
                        mCurrentDeviceSsid = ssid; // 保存当前设备SSID
                        setupWifiAp(ssid); // 设置WiFi接入点
                    }

                    break;
                }

                case Constants.EVENT_SETUP_WIFI_AP: { // WiFi接入点设置事件
                    boolean success = data.getBoolean("success"); // 获取设置结果
                    Log.i(TAG, "Setup wifi ap host success: " + success); // 记录设置结果
                    setupWebSocketServer(); // 设置WebSocket服务器

                    if (!success) { // 如果设置失败
                        Log.e(TAG, "Failed to setup wifi ap host"); // 记录错误日志
                        // showToast(getString(R.string.wifi_ap_failure)); // 注释掉的代码，原本用于显示失败提示
                    }

                    break;
                }

                case Constants.EVENT_STOP_WIFI_AP: { // 停止WiFi接入点事件
                    break; // 当前无操作
                }

                case Constants.EVENT_CONNECTED_DEVICE_INFO: { // 连接设备信息事件
                    String macAddress = data.getString("MAC"); // 获取设备MAC地址
                    updateMacAddress(macAddress); // 更新MAC地址显示
                    break;
                }

                case Constants.EVENT_SETUP_WEBSOCKET: { // WebSocket设置事件
                    try {
                        JSONObject json = new JSONObject(data.getString("result")); // 解析返回的JSON结果
                        Log.i(TAG, "Setup websocket result: " + json); // 记录WebSocket设置结果

                        int clicked = json.optInt("btn_clicked", 0); // 获取按钮点击状态
                        String ipAddress = json.optString("ip_addr", ""); // 获取设备IP地址
                        String macAddress = json.optString("mac_addr", ""); // 获取设备MAC地址
                        String batteryLevel = json.optString("bat_level", ""); // 获取电池电量
                        String firmwareVersion = json.optString("version", ""); // 获取固件版本
                        String rssi = json.optString("rssi", ""); // 获取信号强度

                        if (!firmwareVersion.isEmpty()) { // 如果固件版本不为空
                            updateFirmwareVersion(firmwareVersion); // 更新固件版本显示
                        }

                        if (!batteryLevel.isEmpty()) { // 如果电池电量不为空
                            updateBatteryLevel(batteryLevel.trim()); // 更新电池电量显示
                        }

                        if (!rssi.isEmpty()) { // 如果信号强度不为空
                            updateRssi(rssi); // 更新信号强度显示
                        }

                        if (!ipAddress.isEmpty()) { // 如果IP地址不为空
                            updateIpAddress(ipAddress); // 更新IP地址显示
                        }

                        if (!macAddress.isEmpty()) { // 如果MAC地址不为空
                            updateMacAddress(macAddress); // 更新MAC地址显示
                        }

                        if (clicked == 1) { // 如果设备上的按钮被点击
                            mockAIButtonClicked(true); // 模拟AI按钮点击
                        }

                        if (!mAudioStreaming && !batteryLevel.isEmpty()) { // 如果未进行音频流传输且电池电量不为空
                            if (shouldNotifyLowBattery()) { // 如果应该通知低电量
                                int batLevel = Integer.parseInt(batteryLevel.trim()); // 解析电池电量
                                if (batLevel <= LowBatteryLevel) { // 如果电池电量低于阈值
                                    playHintNotification(R.raw.hint_low_battery); // 播放低电量提示音
                                }
                            }
                        }
                    } catch (NumberFormatException | JSONException e) { // 捕获数字格式异常和JSON解析异常
                        e.printStackTrace(); // 打印异常堆栈
                    }
                    break;
                }

                case Constants.EVENT_HTTP_REQUEST: { // HTTP请求事件
                    boolean success = data.getBoolean("success"); // 获取请求是否成功

                    RequestType requestType = RequestType.values()[msg.arg1]; // 获取请求类型
                    switch (requestType) { // 根据请求类型执行不同操作
                        case TYPE_FRAME_SIZE: // 帧大小设置请求
                            int resId = success ? R.string.set_frame_size_success : R.string.set_frame_size_failure; // 根据结果选择提示消息
                            showToast(getString(resId)); // 显示设置结果提示
                            break;

                        case TYPE_STATUS: // 状态查询请求
                            if (success) { // 如果请求成功
                                try {
                                    JSONObject json = new JSONObject(data.getString("result")); // 解析返回的JSON结果
                                    int frameSize = Integer.parseInt(json.get("framesize").toString()); // 获取帧大小
                                    showStatus(frameSize); // 显示状态信息
                                    updateConnectionStatus(true); // 更新连接状态为已连接
                                } catch (NumberFormatException | JSONException e) { // 捕获数字格式异常和JSON解析异常
                                    e.printStackTrace(); // 打印异常堆栈
                                }
                            } else { // 如果请求失败
                                Log.e(TAG, "Failed to get remote status"); // 记录错误日志
                            }
                            break;
                    }
                    break;
                }

                case Constants.EVENT_CAPTURE_IMAGE: { // 图像捕获事件
                    boolean success = data.getBoolean("success"); // 获取捕获是否成功
                    if (success) { // 如果捕获成功
                        Bitmap bitmap = data.getParcelable("bitmap"); // 获取捕获的位图
                        long elapsed = data.getLong("elapsed"); // 获取捕获耗时
                        showCapture(bitmap, elapsed); // 显示捕获的图像
                    }

                    break;
                }

                case Constants.EVENT_STOP_VIDEO_STREAMING: { // 停止视频流事件
                    if (mVideoStreaming) { // 如果正在进行视频流传输
                        toggleVideoRecording(-1); // 切换视频录制状态为停止
                    }
                    break;
                }

                case Constants.EVENT_STREAMING_ERROR: { // 流传输错误事件
                    toggleErrorAlert(true); // 显示错误提示
                    break;
                }

                case Constants.EVENT_GPT_STREAM_VIDEO: { // GPT视频流事件
                    toggleProcessingStatus(false); // 关闭处理中状态显示

                    Bitmap bitmap = data.getParcelable("bitmap"); // 获取视频帧位图
                    boolean bubbleShow = data.getBoolean("bubbleShow", false); // 获取是否显示气泡
                    if (bitmap != null) { // 如果位图不为空
                        long time = data.getLong("time"); // 获取处理时间
                        boolean updateText = data.getBoolean("updateText"); // 获取是否更新文本
                        showImage(bitmap, time, updateText, bubbleShow); // 显示图像
                    } else { // 如果位图为空
                        resetImage(); // 重置图像显示
                    }

                    break;
                }

                case Constants.EVENT_GPT_PROCESSING: { // GPT处理中事件
                    toggleProcessingStatus(true); // 显示处理中状态
                    break;
                }

                case Constants.EVENT_GPT_TAKE_PHOTO: { // GPT拍照事件
                    toggleProcessingStatus(false); // 关闭处理中状态显示
                    requestOneImage(); // 请求捕获一张图像
                    break;
                }

                case Constants.EVENT_GPT_TAKE_VIDEO: { // GPT录制视频事件
                    toggleProcessingStatus(false); // 关闭处理中状态显示

                    int duration = data.getInt("duration"); // 获取录制时长
                    if ((duration >= 0 && mVideoStreaming) // 如果要开始录制但已经在录制中
                            || (duration < 0 && !mVideoStreaming)) { // 或者要停止录制但没有在录制中
                        Log.i(TAG, "Repeated action"); // 记录重复操作日志
                    } else { // 否则执行录制操作
                        toggleVideoRecording(duration); // 切换视频录制状态
                    }
                    break;
                }

                case Constants.EVENT_GPT_UPLOAD_PHOTO: { // GPT上传照片事件
                    if (!allowUploadPhotos()) { // 如果不允许上传照片
                        break; // 退出处理
                    }

                    String convId = data.getString("conv_id", ""); // 获取会话ID
                    toggleProcessingStatus(false); // 关闭处理中状态显示
                    startGptVideoStreamSession(convId, false, false, new long[0]); // 启动GPT视频流会话，上传实时照片

                    break;
                }

                case Constants.EVENT_GPT_UPLOAD_CAPTURED_PHOTO: { // GPT上传已捕获照片事件
                    if (!allowUploadPhotos()) { // 如果不允许上传照片
                        break; // 退出处理
                    }

                    String convId = data.getString("conv_id", ""); // 获取会话ID
                    toggleProcessingStatus(false); // 关闭处理中状态显示
                    startGptVideoStreamSession(convId, false, true, new long[0]); // 启动GPT视频流会话，上传已捕获照片

                    break;
                }

                case Constants.EVENT_GPT_UPLOAD_VIDEO: { // GPT上传视频事件
                    if (!allowUploadPhotos()) { // 如果不允许上传照片
                        break; // 退出处理
                    }

                    String convId = data.getString("conv_id", ""); // 获取会话ID
                    long[] frames = data.getLongArray("frames"); // 获取帧时间戳数组
                    toggleProcessingStatus(false); // 关闭处理中状态显示
                    startGptVideoStreamSession(convId, true, false, frames); // 启动GPT视频流会话，上传实时视频

                    break;
                }

                case Constants.EVENT_GPT_UPLOAD_RECORDED_VIDEO: { // GPT上传已录制视频事件
                    if (!allowUploadPhotos()) { // 如果不允许上传照片
                        break; // 退出处理
                    }

                    String convId = data.getString("conv_id", ""); // 获取会话ID
                    long[] frames = data.getLongArray("frames"); // 获取帧时间戳数组
                    toggleProcessingStatus(false); // 关闭处理中状态显示
                    startGptVideoStreamSession(convId, true, !mVideoStreaming, frames); // 启动GPT视频流会话，上传已录制视频

                    break;
                }

                case Constants.EVENT_GPT_COMPLETE: // GPT完成事件
                case Constants.EVENT_GPT_RECEIVED_QA: // GPT接收问答事件
                case Constants.EVENT_GPT_DISRUPT: { // GPT中断事件
                    toggleProcessingStatus(false); // 关闭处理中状态显示
                    break;
                }

                case Constants.EVENT_GPT_QA_QUERY: { // GPT问答查询事件
                    toggleProcessingStatus(false); // 关闭处理中状态显示
                    String text = data.getString("text"); // 获取查询文本
                    appendQAText(text, true, null); // 添加查询文本到问答区域
                    break;
                }

                case Constants.EVENT_GPT_QA_ANSWER: { // GPT问答回答事件
                    toggleProcessingStatus(false); // 关闭处理中状态显示
                    String text = data.getString("text"); // 获取回答文本
                    appendQAText(text, false, null); // 添加回答文本到问答区域
                    break;
                }

                case Constants.EVENT_GPT_ERROR: { // GPT错误事件
                    toggleProcessingStatus(false); // 关闭处理中状态显示
                    toggleErrorAlert(true); // 显示错误提示
                    break;
                }

                case Constants.EVENT_GPT_EXIT: { // GPT退出事件
                    toggleProcessingStatus(false); // 关闭处理中状态显示
                    if (mAudioStreaming) { // 如果正在进行音频流传输
                        mockAIButtonClicked(true); // 模拟AI按钮点击，停止音频流
                    }
                    break;
                }

                case Constants.EVENT_GPT_SWITCH_MODE: { // GPT切换模式事件
                    toggleProcessingStatus(false); // 关闭处理中状态显示
                    int mode = data.getInt("user_mode"); // 获取用户模式
                    String convId = data.getString("conv_id", ""); // 获取会话ID
                    UserMode userMode = UserMode.values()[mode]; // 获取用户模式枚举值
                    setUserMode(userMode, convId); // 设置用户模式
                    break;
                }
            }
        }
    }

    /**
     * 服务