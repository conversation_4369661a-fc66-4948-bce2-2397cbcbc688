package com.microsoft.mai.smartview.gpt;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * GPT响应监听器接口
 * 用于处理从GPT服务接收到的响应
 * 实现此接口的类可以接收和处理各种GPT操作及其参数
 */
public interface GptResponseListener {
    /**
     * 处理GPT响应的回调方法
     * @param action GPT操作类型，指示需要执行的动作
     * @param params 操作相关的参数，可以是任意类型的数据
     */
    void onResponse(@NonNull GptAction action, @Nullable Object params);
}
