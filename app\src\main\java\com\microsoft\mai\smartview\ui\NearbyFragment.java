package com.microsoft.mai.smartview.ui;

// 导入必要的Android系统库

import android.Manifest; // 导入Android权限相关类
import android.bluetooth.BluetoothAdapter; // 导入蓝牙适配器类，用于管理蓝牙功能
import android.bluetooth.BluetoothDevice; // 导入蓝牙设备类，表示远程蓝牙设备
import android.content.BroadcastReceiver; // 导入广播接收器类，用于接收系统广播
import android.content.Context; // 导入上下文类，提供应用环境信息
import android.content.Intent; // 导入意图类，用于启动活动和服务
import android.content.IntentFilter; // 导入意图过滤器类，用于过滤广播
import android.content.pm.PackageManager; // 导入包管理器类，用于检查权限
import android.os.Bundle; // 导入Bundle类，用于传递数据
import android.util.Log; // 导入日志类，用于记录调试信息
import android.view.LayoutInflater; // 导入布局填充器类，用于加载XML布局
import android.view.View; // 导入视图类，UI组件的基类
import android.view.ViewGroup; // 导入视图组类，容纳其他视图的容器
import android.widget.AdapterView; // 导入适配器视图类，用于显示列表数据
import android.widget.ArrayAdapter; // 导入数组适配器类，用于绑定数据到列表
import android.widget.TextView; // 导入文本视图类，用于显示文本
import androidx.appcompat.widget.Toolbar; // 导入工具栏类，用于显示应用栏

import androidx.annotation.RequiresPermission; // 导入权限注解类，用于标记需要特定权限的方法
import androidx.annotation.NonNull; // 导入非空注解类，用于标记不能为空的参数
import androidx.appcompat.app.ActionBar; // 导入操作栏类，用于管理应用栏
import androidx.appcompat.app.AppCompatActivity; // 导入兼容性活动类，提供向后兼容的功能
import androidx.core.app.ActivityCompat; // 导入活动兼容类，用于处理权限请求
import androidx.fragment.app.Fragment; // 导入片段类，UI的可重用部分

import com.microsoft.mai.smartview.MainActivity; // 导入主活动类
import com.microsoft.mai.smartview.R; // 导入资源类，用于访问应用资源
import com.microsoft.mai.smartview.constant.Constants; // 导入常量类，包含应用中使用的常量
import com.microsoft.mai.smartview.databinding.DeviceListItemBinding; // 导入设备列表项绑定类，用于视图绑定
import com.microsoft.mai.smartview.databinding.FragmentNearbyBinding; // 导入附近片段绑定类，用于视图绑定
import com.microsoft.mai.smartview.utils.StringUtils; // 导入字符串工具类，提供字符串处理方法

import java.util.ArrayList; // 导入数组列表类，用于存储动态数组

/**
 * 附近设备片段类
 * 用于发现和连接附近的蓝牙设备，特别是SmartSense设备
 * 显示可用的蓝牙设备列表，并允许用户选择和连接设备
 */
public class NearbyFragment extends Fragment {
    private static final String TAG = "NearbyFragment"; // 日志标签，用于调试和日志记录

    private FragmentNearbyBinding binding; // 视图绑定对象，用于访问布局中的视图元素

    private BluetoothAdapter mBtAdapter; // 蓝牙适配器，用于管理设备的蓝牙功能

    private final ArrayList<Device> mPairedDeviceList = new ArrayList<>(); // 已配对设备列表
    private DeviceAdapter mPairedDevicesAdapter; // 已配对设备的适配器，用于显示已配对设备

    private final ArrayList<Device> mNewDeviceList = new ArrayList<>(); // 新发现的设备列表
    private DeviceAdapter mNewDevicesAdapter; // 新设备的适配器，用于显示新发现的设备

    /**
     * 创建片段时调用
     * 初始化蓝牙适配器，注册广播接收器，设置操作栏
     * @param savedInstanceState 保存的实例状态
     */
    @SuppressWarnings({"deprecation"}) // 忽略使用已弃用API的警告
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState); // 调用父类的onCreate方法

        // 获取默认的蓝牙适配器
        mBtAdapter = BluetoothAdapter.getDefaultAdapter();

        // 创建意图过滤器，用于接收蓝牙相关的广播
        IntentFilter filter = new IntentFilter(BluetoothDevice.ACTION_FOUND); // 添加设备发现动作
        filter.addAction(BluetoothDevice.ACTION_BOND_STATE_CHANGED); // 添加设备绑定状态变化动作
        filter.addAction(BluetoothDevice.ACTION_ACL_CONNECTED); // 添加设备连接动作
        filter.addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED); // 添加设备断开连接动作
        filter.addAction(BluetoothAdapter.ACTION_DISCOVERY_FINISHED); // 添加设备发现完成动作

        // 注册广播接收器，用于接收蓝牙相关的广播
        requireContext().getApplicationContext().registerReceiver(mReceiver, filter);

        // 如果蓝牙未启用，则请求用户启用蓝牙
        if (!mBtAdapter.isEnabled()) {
            Intent enableBtIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
            startActivityForResult(enableBtIntent, 1); // 启动请求启用蓝牙的活动
        }

        // 设置操作栏的返回按钮
        final ActionBar actionBar = ((AppCompatActivity)requireActivity()).getSupportActionBar();
        actionBar.setDisplayHomeAsUpEnabled(true); // 显示返回按钮

        // 设置工具栏的导航点击监听器
        final Toolbar toolbar = requireActivity().getWindow().getDecorView().findViewById(R.id.toolbar);
        toolbar.setNavigationOnClickListener(v -> requireActivity().onBackPressed()); // 点击返回按钮时返回上一页
    }

    /**
     * 创建视图时调用
     * 初始化视图绑定，设置适配器和列表视图属性
     * @param inflater 布局填充器
     * @param container 父视图容器
     * @param savedInstanceState 保存的实例状态
     * @return 创建的视图
     */
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        // 使用视图绑定加载布局
        binding = FragmentNearbyBinding.inflate(inflater, container, false);

        // 创建已配对设备和新设备的适配器
        mPairedDevicesAdapter = new DeviceAdapter(getContext(), mPairedDeviceList);
        mNewDevicesAdapter = new DeviceAdapter(getContext(), mNewDeviceList);

        // 设置新设备列表的适配器
        binding.newDevices.setAdapter(mNewDevicesAdapter);
        // 设置列表为空时显示的视图
        binding.newDevices.setEmptyView(binding.newEmptyView);

        // 设置列表项点击监听器
        binding.newDevices.setOnItemClickListener(mDeviceClickListener);
        // 启用平滑滚动条
        binding.newDevices.setSmoothScrollbarEnabled(true);
        // 启用垂直滚动条
        binding.newDevices.setVerticalScrollBarEnabled(true);

        // 返回根视图
        return binding.getRoot();
    }

    /**
     * 片段恢复时调用
     * 开始发现蓝牙设备
     * 需要蓝牙扫描和连接权限
     */
    @RequiresPermission(allOf = {Manifest.permission.BLUETOOTH_SCAN, Manifest.permission.BLUETOOTH_CONNECT})
    @Override
    public void onResume() {
        super.onResume(); // 调用父类的onResume方法
        doDiscovery(); // 开始发现蓝牙设备
    }

    /**
     * 片段暂停时调用
     */
    @Override
    public void onPause() {
        super.onPause(); // 调用父类的onPause方法
    }

    /**
     * 视图销毁时调用
     * 清除视图绑定，取消蓝牙设备发现
     * 需要蓝牙扫描权限
     */
    @RequiresPermission(Manifest.permission.BLUETOOTH_SCAN)
    @Override
    public void onDestroyView() {
        super.onDestroyView(); // 调用父类的onDestroyView方法
        binding = null; // 清除视图绑定，避免内存泄漏

        // 如果蓝牙适配器正在发现设备，则取消发现
        if (mBtAdapter != null && mBtAdapter.isDiscovering()) {
            mBtAdapter.cancelDiscovery();
        }
    }

    /**
     * 片段销毁时调用
     * 注销广播接收器
     */
    @Override
    public void onDestroy() {
        super.onDestroy(); // 调用父类的onDestroy方法
        // 注销广播接收器，避免内存泄漏
        requireContext().getApplicationContext().unregisterReceiver(mReceiver);
    }

    /**
     * 开始发现蓝牙设备
     * 需要蓝牙扫描权限
     */
    @RequiresPermission(Manifest.permission.BLUETOOTH_SCAN)
    private void doDiscovery() {
        Log.d(TAG, "doDiscovery"); // 记录调试日志

        // 检查是否有蓝牙扫描权限
        if (ActivityCompat.checkSelfPermission(requireContext(), Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED) {
            Log.e(TAG, "Error for doDiscovery, not BLUETOOTH_SCAN permission granted"); // 记录错误日志
            return; // 如果没有权限，则返回
        }

        // 如果已经在发现设备，则不重复启动
        if (mBtAdapter.isDiscovering()) {
            // mBtAdapter.cancelDiscovery(); // 注释掉的代码，原本用于取消当前的发现过程
            return;
        }

        // mNewDeviceList.clear(); // 注释掉的代码，原本用于清除新设备列表
        mBtAdapter.startDiscovery(); // 开始发现蓝牙设备
    }

    /**
     * 设备列表项点击监听器
     * 处理用户点击设备列表项的事件
     */
    private final AdapterView.OnItemClickListener mDeviceClickListener = new AdapterView.OnItemClickListener() {
        /**
         * 列表项点击事件处理方法
         * 需要蓝牙扫描和连接权限
         * @param av 适配器视图
         * @param v 被点击的视图
         * @param position 被点击项的位置
         * @param id 被点击项的ID
         */
        @RequiresPermission(allOf = {Manifest.permission.BLUETOOTH_SCAN, Manifest.permission.BLUETOOTH_CONNECT})
        public void onItemClick(AdapterView<?> av, View v, int position, long id) {
            mBtAdapter.cancelDiscovery(); // 取消设备发现，避免影响连接过程
            Device device = mNewDeviceList.get(position); // 获取被点击的设备

            // 如果设备名称以SmartSense-开头，则连接该设备
            if (device.name != null && device.name.startsWith(Constants.SMART_DEVICE_NAME_PREFIX)) {
                Log.i(TAG, "select device: " + device.name); // 记录选择的设备
                connectDevice(device.name); // 连接设备
            } else {
                device.startBond(); // 否则开始与设备配对
            }
        }
    };

    /**
     * 连接设备
     * 根据设备名称生成WiFi热点SSID，并导航到主页
     * @param deviceName 设备名称
     */
    private void connectDevice(@NonNull String deviceName) {
        // 根据设备名称生成WiFi热点SSID
        final String ssid = StringUtils.getWiFiApSsid(deviceName);
        // 导航到主页，并传递SSID参数
        ((MainActivity) requireActivity()).navigateToHome(ssid);
    }

    /**
     * 蓝牙广播接收器
     * 用于接收蓝牙相关的广播事件，如设备发现、连接状态变化等
     */
    private final BroadcastReceiver mReceiver = new BroadcastReceiver() {
        /**
         * 接收广播事件的回调方法
         * @param context 上下文
         * @param intent 包含广播信息的意图
         */
        @Override
        public void onReceive(Context context, @NonNull Intent intent) {
            // 检查是否有蓝牙扫描和连接权限
            if (ActivityCompat.checkSelfPermission(requireContext(), Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED
                    || ActivityCompat.checkSelfPermission(requireContext(), Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                return; // 如果没有权限，则返回
            }

            String action = intent.getAction(); // 获取广播动作
            if (BluetoothDevice.ACTION_FOUND.equals(action)) { // 如果是设备发现动作
                // 从意图中获取蓝牙设备
                final BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                // 如果设备有效、有名称且未配对
                if (device != null && device.getName() != null && device.getBondState() != BluetoothDevice.BOND_BONDED) {
                    // 如果设备已经在列表中，则跳过
                    if (mNewDevicesAdapter.containsDevice(device.getAddress())) {
                        return;
                    }

                    // 在UI线程上更新设备列表
                    requireActivity().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            // 再次检查权限，因为可能在UI线程执行前已经失去权限
                            if (ActivityCompat.checkSelfPermission(requireContext(), Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED
                                    || ActivityCompat.checkSelfPermission(requireContext(), Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                                return;
                            }

                            // 添加新设备到适配器
                            mNewDevicesAdapter.add(new Device(device.getName(), device.getAddress(), "", device));
                            // 对设备列表进行排序，使SmartSense设备排在前面
                            mNewDevicesAdapter.sort((o1, o2) -> {
                                if (o1.name.startsWith(Constants.SMART_DEVICE_NAME_PREFIX)) { // 如果第一个设备是SmartSense设备
                                    if (o2.name.startsWith(Constants.SMART_DEVICE_NAME_PREFIX)) { // 如果第二个设备也是SmartSense设备
                                        return o1.name.compareTo(o2.name); // 按名称字母顺序排序
                                    } else {
                                        return -1; // SmartSense设备排在前面
                                    }
                                } else if (o2.name.startsWith(Constants.SMART_DEVICE_NAME_PREFIX

    /**
     * 设备类
     * 用于存储蓝牙设备的信息和操作
     */
    private static class Device {
        String name; // 设备名称
        String macAddress; // 设备MAC地址
        String status; // 设备状态

        BluetoothDevice device; // 蓝牙设备对象

        /**
         * 构造函数
         * @param name 设备名称
         * @param macAddress 设备MAC地址
         * @param status 设备状态
         * @param device 蓝牙设备对象
         */
        public Device(String name, String macAddress, String status, BluetoothDevice device) {
            this.name = name;
            this.macAddress = macAddress;
            this.status = status;
            this.device = device;
        }

        /**
         * 开始与设备配对
         * 需要蓝牙连接权限
         */
        @RequiresPermission(Manifest.permission.BLUETOOTH_CONNECT)
        public void startBond() {
            if (device == null) {
                return; // 如果设备对象为空，则返回
            }

            if (device.getBondState() == BluetoothDevice.BOND_NONE) {
                device.createBond(); // 如果设备未配对，则创建配对
            }
        }
    }

    /**
     * 设备适配器类
     * 用于将设备数据绑定到列表视图
     */
    class DeviceAdapter extends ArrayAdapter<Device> {

        /**
         * 视图持有者类
         * 用于缓存列表项中的视图，提高列表性能
         */
        private class ViewHolder {
            TextView name; // 设备名称文本视图
            TextView macAddress; // 设备MAC地址文本视图
            TextView status; // 设备状态文本视图
        }

        /**
         * 构造函数
         * @param context 上下文
         * @param devices 设备列表
         */
        DeviceAdapter(Context context, ArrayList<Device> devices) {
            super(context, R.layout.device_list_item, devices);
        }

        /**
         * 获取列表项视图
         * @param position 位置
         * @param convertView 可重用的视图
         * @param parent 父视图
         * @return 列表项视图
         */
        @NonNull
        @Override
        public View getView(int position, View convertView, @NonNull ViewGroup parent) {
            Device device = getItem(position); // 获取当前位置的设备
            ViewHolder viewHolder; // 视图持有者

            // 如果没有可重用的视图，则创建新视图
            if (convertView == null) {
                // 使用布局填充器加载列表项布局
                LayoutInflater inflater = LayoutInflater.from(requireContext());
                com.microsoft.mai.smartview.databinding.DeviceListItemBinding binding = DeviceListItemBinding.inflate(inflater, parent, false);

                // 创建视图持有者并绑定视图
                viewHolder = new ViewHolder();
                viewHolder.name = binding.deviceName;
                viewHolder.status = binding.status;
                viewHolder.macAddress = binding.macAddress;

                // 设置视图和标签
                convertView = binding.getRoot();
                convertView.setTag(viewHolder);
            } else {
                // 如果有可重用的视图，则获取其视图持有者
                viewHolder = (ViewHolder) convertView.getTag();
            }

            // 确保设备不为空
            assert device != null;
            // 设置设备信息到视图
            viewHolder.name.setText(device.name);
            viewHolder.status.setText(device.status);
            viewHolder.macAddress.setText(device.macAddress);

            return convertView; // 返回列表项视图
        }

        /**
         * 检查适配器是否包含指定MAC地址的设备
         * @param macAddress 设备MAC地址
         * @return 如果包含则返回true，否则返回false
         */
        public boolean containsDevice(@NonNull String macAddress) {
            int size = getCount(); // 获取设备数量
            for (int i = 0; i < size; i++) { // 遍历所有设备
                Device device = getItem(i); // 获取当前位置的设备
                if (device.macAddress.equalsIgnoreCase(macAddress)) { // 比较MAC地址（忽略大小写）
                    return true; // 如果找到匹配的设备，则返回true
                }
            }

            return false; // 如果没有找到匹配的设备，则返回false
        }
    }
}