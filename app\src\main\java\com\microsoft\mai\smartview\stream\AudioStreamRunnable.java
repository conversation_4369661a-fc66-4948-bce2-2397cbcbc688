package com.microsoft.mai.smartview.stream;

import android.os.Bundle;
import android.os.Message;
import android.os.Messenger;
import android.os.RemoteException;
import android.util.Log;

import androidx.annotation.NonNull;

import com.microsoft.mai.smartview.constant.Constants;
import com.microsoft.mai.smartview.constant.UserMode;
import com.microsoft.mai.smartview.gpt.GptAction;
import com.microsoft.mai.smartview.gpt.GptBinaryStream;
import com.microsoft.mai.smartview.gpt.GptBinaryType;

import org.jetbrains.annotations.Contract;

import java.net.URL;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 音频流处理线程类
 * 负责接收设备音频数据流，发送至GPT服务，并处理GPT返回的各种指令
 * 继承自GptResponseAudio，支持音频播放回调
 */
public final class AudioStreamRunnable extends GptResponseAudio {
    private static final String TAG = "AudioSteamThread";

    // 停止音频流处理的标志
    private volatile boolean mStopAudioStream = false;
    // 用于线程同步的计数器
    private CountDownLatch mCountDownLatch = new CountDownLatch(1);

    // 设备数据流URL
    private final URL mURL;
    // 设备数据流监听器，用于接收音频数据
    private final DeviceStreamSubscriber.DeviceStreamDataListener mStreamDataListener;
    // 音频数据队列，存储待处理的音频数据包
    private final BlockingQueue<byte[]> queue = new LinkedBlockingQueue<>();
    // GPT二进制流连接，用于与GPT服务器通信
    private final GptBinaryStream mGptBinaryStream;

    // 是否正在处理中标志
    private boolean mProcessing = false;
    // 是否已收到GPT问答响应标志
    private boolean mReceivedGptQA = false;
    // 是否由用户主动停止标志
    private boolean mStoppedByUser = false;
    // 当前会话ID
    private String mCurrentConversationId = "";

    /**
     * 构造函数
     * @param messenger 与UI线程通信的Messenger对象
     * @param deviceURL 设备音频流URL
     */
    public AudioStreamRunnable(@NonNull Messenger messenger,
                               @NonNull URL deviceURL) {
        super(true, Constants.RESPONSE_AUDIO_MP3);

        mURL = deviceURL;
        // 创建设备数据流监听器
        mStreamDataListener = new DeviceStreamSubscriber.DeviceStreamDataListener() {
            /**
             * 数据接收回调
             * 将收到的音频数据加入队列
             */
            @Override
            public void onDataReceived(@NonNull byte[] data) {
                queue.offer(data);
            }

            /**
             * 错误回调
             * 处理音频流传输中的错误
             */
            @Override
            public void onError(@NonNull Throwable t) {
                Log.e(TAG, "Error for streaming: " + deviceURL + ", error message: " + t.getMessage());

                // 发送错误消息到UI线程
                final Message msg = Message.obtain();
                msg.what = Constants.EVENT_STREAMING_ERROR;
                try {
                    messenger.send(msg);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }

                // 标记停止并通知等待线程
                mCountDownLatch.countDown();
                mStopAudioStream = true;
            }
        };
        // 注册设备数据流订阅
        DeviceStreamSubscriber.getInstance().addSubscriber(deviceURL, mStreamDataListener);

        // 创建GPT二进制流连接，用于发送音频数据和接收GPT指令
        mGptBinaryStream = new GptBinaryStream(GptBinaryType.BINARY_TYPE_AUDIO,
                false,
                Constants.URL_GPT_AUDIO_STREAM,
                "",
                (action, params) -> {
                    // 收到GPT响应后，重置处理中状态
                    mProcessing = false;

                    // 根据GPT返回的不同操作类型，执行相应处理
                    switch (action) {
                        // 处理模式切换指令，包括通用模式、盲人模式等
                        case GPT_ACTION_SWITCH_MODE_GENERAL:
                        case GPT_ACTION_SWITCH_MODE_BLIND:
                        case GPT_ACTION_SWITCH_MODE_ELDER:
                        case GPT_ACTION_SWITCH_MODE_TRAVELING:
                        case GPT_ACTION_SWITCH_MODE_NATURALIST:
                        case GPT_ACTION_SWITCH_MODE_TRANSLATION:
                        case GPT_ACTION_SWITCH_MODE_MEETING:
                        case GPT_ACTION_SWITCH_MODE_HEALTH: {
                            // 发送模式切换消息到UI线程
                            final Message msg = Message.obtain();
                            final Bundle data = new Bundle();

                            msg.what = Constants.EVENT_GPT_SWITCH_MODE;
                            msg.obj = data;

                            data.putString("conv_id", mCurrentConversationId);
                            data.putInt("user_mode", getUserModeByAction(action).ordinal());

                            try {
                                messenger.send(msg);
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }

                            break;
                        }

                        // 处理拍照指令
                        case GPT_ACTION_TAKE_PHOTO: {
                            final Message msg = Message.obtain();
                            msg.what = Constants.EVENT_GPT_TAKE_PHOTO;
                            try {
                                messenger.send(msg);
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }

                            break;
                        }

                        // 处理录制视频指令，带有时长参数
                        case GPT_ACTION_TAKE_VIDEO: {
                            int duration = (int) params;

                            final Message msg = Message.obtain();
                            final Bundle data = new Bundle();
                            msg.what = Constants.EVENT_GPT_TAKE_VIDEO;
                            msg.obj = data;
                            data.putInt("duration", duration);
                            try {
                                messenger.send(msg);
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }

                            break;
                        }

                        // 处理开始录制视频指令
                        case GPT_ACTION_VIDEO_START: {
                            final Message msg = Message.obtain();
                            final Bundle data = new Bundle();
                            msg.what = Constants.EVENT_GPT_TAKE_VIDEO;
                            msg.obj = data;
                            data.putInt("duration", 0);

                            try {
                                messenger.send(msg);
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }

                            break;
                        }

                        // 处理停止录制视频指令
                        case GPT_ACTION_VIDEO_STOP: {
                            final Message msg = Message.obtain();
                            final Bundle data = new Bundle();
                            msg.what = Constants.EVENT_GPT_TAKE_VIDEO;
                            msg.obj = data;
                            data.putInt("duration", -1);

                            try {
                                messenger.send(msg);
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }

                            break;
                        }

                        // 处理上传照片指令
                        case GPT_ACTION_UPLOAD_PHOTO: {
                            final Message msg = Message.obtain();
                            final Bundle data = new Bundle();
                            data.putString("conv_id", mCurrentConversationId);

                            msg.what = Constants.EVENT_GPT_UPLOAD_PHOTO;
                            msg.obj = data;

                            try {
                                messenger.send(msg);
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }

                            break;
                        }

                        // 处理上传已捕获照片指令
                        case GPT_ACTION_UPLOAD_CAPTURED_PHOTO: {
                            final Message msg = Message.obtain();
                            final Bundle data = new Bundle();
                            data.putString("conv_id", mCurrentConversationId);

                            msg.what = Constants.EVENT_GPT_UPLOAD_CAPTURED_PHOTO;
                            msg.obj = data;

                            try {
                                messenger.send(msg);
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }

                            break;
                        }

                        // 处理上传视频指令
                        case GPT_ACTION_UPLOAD_VIDEO: {
                            final Message msg = Message.obtain();
                            final Bundle data = new Bundle();
                            data.putString("conv_id", mCurrentConversationId);
                            data.putLongArray("frames", (long[])params);

                            msg.what = Constants.EVENT_GPT_UPLOAD_VIDEO;
                            msg.obj = data;

                            try {
                                messenger.send(msg);
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }

                            break;
                        }

                        // 处理上传已录制视频指令
                        case GPT_ACTION_UPLOAD_RECORDED_VIDEO: {
                            final Message msg = Message.obtain();
                            final Bundle data = new Bundle();
                            data.putString("conv_id", mCurrentConversationId);
                            data.putLongArray("frames", (long[])params);

                            msg.what = Constants.EVENT_GPT_UPLOAD_RECORDED_VIDEO;
                            msg.obj = data;

                            try {
                                messenger.send(msg);
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }

                            break;
                        }

                        // 处理问答答案指令
                        case GPT_ACTION_QA_ANSWER: {
                            if (mStopAudioStream) {
                                break;
                            }

                            final Message msg = Message.obtain();
                            final Bundle data = new Bundle();
                            data.putString("text", (String) params);

                            msg.what = Constants.EVENT_GPT_QA_ANSWER;
                            msg.obj = data;
                            try {
                                messenger.send(msg);
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }

                            break;
                        }

                        // 处理二进制数据问答指令
                        case GPT_ACTION_QA_BINARY: {
                            if (!mReceivedGptQA) {
                                final Message msg = Message.obtain();
                                msg.what = Constants.EVENT_GPT_RECEIVED_QA;
                                try {
                                    messenger.send(msg);
                                } catch (RemoteException e) {
                                    e.printStackTrace();
                                }

                                mReceivedGptQA = true;
                            }

                            if (mStoppedByUser || mStopAudioStream) {
                                break;
                            }

                            byte[] audio = (byte[]) params;
                            doReceivedAudioData(audio);
                            break;
                        }

                        // 处理问答查询指令
                        case GPT_ACTION_QA_QUERY: {
                            if (mStopAudioStream) {
                                break;
                            }

                            final Message msg = Message.obtain();
                            final Bundle data = new Bundle();
                            data.putString("text", (String) params);

                            msg.what = Constants.EVENT_GPT_QA_QUERY;
                            msg.obj = data;
                            try {
                                messenger.send(msg);
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }

                            break;
                        }

                        // 处理处理中状态指令
                        case GPT_ACTION_PROCESSING: {
                            mReceivedGptQA = false;
                            mProcessing = true;
                            mStoppedByUser = false;

                            final Message msg = Message.obtain();
                            msg.what = Constants.EVENT_GPT_PROCESSING;
                            try {
                                messenger.send(msg);
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }

                            break;
                        }

                        // 处理已连接状态指令
                        case GPT_ACTION_CONNECTED: {
                            mCurrentConversationId = (String)params;
                            break;
                        }

                        // 处理中断指令
                        case GPT_ACTION_DISRUPT: {
                            mStoppedByUser = true;

                            final Bundle data = new Bundle();
                            data.putString("text", (String) params);

                            final Message msg = Message.obtain();
                            msg.what = Constants.EVENT_GPT_DISRUPT;
                            msg.obj = data;
                            try {
                                messenger.send(msg);
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }

                            stopAudioPlaying();
                            break;
                        }

                        // 处理完成指令
                        case GPT_ACTION_COMPLETE: {
                            final Message msg = Message.obtain();
                            msg.what = Constants.EVENT_GPT_COMPLETE;
                            try {
                                messenger.send(msg);
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }
                            break;
                        }

                        // 处理退出指令
                        case GPT_ACTION_EXIT: {
                            mStoppedByUser = true;

                            final Message msg = Message.obtain();
                            msg.what = Constants.EVENT_GPT_EXIT;
                            try {
                                messenger.send(msg);
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }

                            stopAudioPlaying();
                            break;
                        }

                        // 处理错误指令
                        case GPT_ACTION_ERROR: {
                            mStopAudioStream = true;
                            mCountDownLatch.countDown();

                            final Message msg = Message.obtain();
                            msg.what = Constants.EVENT_GPT_ERROR;
                            try {
                                messenger.send(msg);
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }

                            break;
                        }
                    }
                });
    }

    /**
     * 停止音频流处理
     * 清理资源并通知线程结束
     */
    public void stopAudioStream() {
        mStoppedByUser = true;
        mStopAudioStream = true;
        mCountDownLatch.countDown();
    }

    /**
     * 线程执行方法
     * 循环从队列中获取音频数据并发送到GPT服务
     */
    @Override
    public void run() {
        // 循环处理音频数据
        while (!mStopAudioStream) {
            try {
                // 从队列中获取音频数据，超时时间1秒
                byte[] data = queue.poll(1000, TimeUnit.MILLISECONDS);
                if (data == null) {
                    continue;
                }

                if (!mProcessing) {
                    // 如果当前没有处理中的请求，标记处理中并发送数据
                    mProcessing = true;
                    mGptBinaryStream.send(data);
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
                break;
            }
        }

        // 关闭GPT二进制流连接
        mGptBinaryStream.close();

        try {
            // 等待最后的处理完成
            mCountDownLatch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        } finally {
            // 取消设备数据流订阅
            DeviceStreamSubscriber.getInstance().removeSubscriber(mURL, mStreamDataListener);
        }
    }

    /**
     * 根据GPT操作获取对应的用户模式
     * 将GPT操作映射到UserMode枚举
     * @param action GPT操作类型
     * @return 对应的用户模式
     */
    @Contract(pure = true)
    private static UserMode getUserModeByAction(@NonNull GptAction action) {
        // 根据不同的GPT操作返回对应的用户模式
        switch (action) {
            case GPT_ACTION_SWITCH_MODE_GENERAL:
                return UserMode.MODE_GENERAL;
            case GPT_ACTION_SWITCH_MODE_BLIND:
                return UserMode.MODE_BLIND;
            case GPT_ACTION_SWITCH_MODE_ELDER:
                return UserMode.MODE_ELDER;
            case GPT_ACTION_SWITCH_MODE_TRAVELING:
                return UserMode.MODE_TRAVELING;
            case GPT_ACTION_SWITCH_MODE_NATURALIST:
                return UserMode.MODE_NATURALIST;
            case GPT_ACTION_SWITCH_MODE_TRANSLATION:
                return UserMode.MODE_TRANSLATION;
            case GPT_ACTION_SWITCH_MODE_MEETING:
                return UserMode.MODE_MEETING;
            case GPT_ACTION_SWITCH_MODE_HEALTH:
                return UserMode.MODE_HEALTH;
            default:
                return UserMode.MODE_UNKNOWN;
        }
    }
}
