package com.microsoft.mai.smartview;

import android.app.Application;
import android.os.Environment;
import android.util.Log;

import com.microsoft.mai.smartview.constant.Constants;
import com.microsoft.mai.smartview.constant.UserMode;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * 应用程序类
 * 负责应用程序的全局初始化工作，包括：
 * 1. 创建必要的存储目录
 * 2. 初始化配置信息
 * 3. 管理用户模式
 */
public class SmartViewApplication extends Application {
    private final static String TAG = "SmartViewApplication";

    // 应用程序单例实例
    private static SmartViewApplication instance;
    // 当前用户模式
    private UserMode mUserMode = UserMode.MODE_GENERAL;

    /**
     * 应用程序创建时调用
     * 初始化存储目录和配置信息
     */
    @Override
    public void onCreate() {
        super.onCreate();
        instance = this;
        initFolders();
        initKeys();
    }

    /**
     * 获取当前用户模式
     * @return 当前用户模式
     */
    public UserMode getCurrentUserMode() {
        return mUserMode;
    }

    /**
     * 设置当前用户模式
     * @param userMode 要设置的用户模式
     */
    public void setCurrentUserMode(UserMode userMode) {
        mUserMode = userMode;
    }

    /**
     * 初始化配置信息
     * 从raw资源文件中读取salt和bypass配置
     */
    private void initKeys() {
        try {
            final BufferedReader reader = new BufferedReader(new InputStreamReader(getResources().openRawResource(R.raw.config)));
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.startsWith("salt:")) {
                    Constants.GPT_SALT = line.substring(5).trim();
                } else if (line.startsWith("bypass:")) {
                    Constants.WIFI_AP_PASSWORD = line.substring(7).trim();
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取应用程序单例实例
     * @return 应用程序实例
     */
    public static SmartViewApplication getInstance() {
        return instance;
    }

    /**
     * 初始化存储目录
     * 在外部存储中创建videos和images目录
     */
    private void initFolders() {
        final String rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM).toString();
        final File videoDir = new File(rootPath + "/SmartView/videos");
        if (!videoDir.exists()) {
            final boolean success = videoDir.mkdirs();
            if (!success) {
                Log.e(TAG, "Failed to create folder: " + videoDir.getPath());
                if (isExternalStorageReadOnly() || isExternalStorageNotAvailable()) {
                    Log.e(TAG, "Read only folder: " + videoDir.getPath());
                }
            }
        }

        final File imageDir = new File(rootPath + "/SmartView/images");
        if (!imageDir.exists()) {
            final boolean success = imageDir.mkdirs();
            if (!success) {
                Log.e(TAG, "Failed to create folder: " + imageDir.getPath());
                if (isExternalStorageReadOnly() || isExternalStorageNotAvailable()) {
                    Log.e(TAG, "Read only folder: " + imageDir.getPath());
                }
            }
        }
    }

    /**
     * 检查外部存储是否为只读状态
     * @return 如果外部存储为只读则返回true
     */
    private static boolean isExternalStorageReadOnly() {
        final String extStorageState = Environment.getExternalStorageState();
        return Environment.MEDIA_MOUNTED_READ_ONLY.equals(extStorageState);
    }

    /**
     * 检查外部存储是否可用
     * @return 如果外部存储不可用则返回true
     */
    private static boolean isExternalStorageNotAvailable() {
        final String extStorageState = Environment.getExternalStorageState();
        return !Environment.MEDIA_MOUNTED.equals(extStorageState);
    }
}
