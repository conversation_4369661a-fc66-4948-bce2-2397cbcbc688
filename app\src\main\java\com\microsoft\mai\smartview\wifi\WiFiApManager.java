package com.microsoft.mai.smartview.wifi;

import android.Manifest;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.net.wifi.WifiManager;
import android.net.wifi.p2p.WifiP2pConfig;
import android.net.wifi.p2p.WifiP2pDevice;
import android.net.wifi.p2p.WifiP2pDeviceList;
import android.net.wifi.p2p.WifiP2pGroup;
import android.net.wifi.p2p.WifiP2pManager;
import android.os.Build;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.annotation.RequiresPermission;
import androidx.core.app.ActivityCompat;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * WiFi热点管理器
 * 负责管理WiFi Direct热点功能，包括：
 * 1. 创建和管理WiFi Direct热点
 * 2. 监控连接设备变化
 * 3. 处理WiFi Direct状态变化
 */
public class WiFiApManager {
    private static final String TAG = "WiFiApManager";
    private static WiFiApManager instance;

    // 上下文对象
    private final Context mContext;
    // WiFi管理器
    private final WifiManager mWifiManager;
    // WiFi Direct管理器
    private final WifiP2pManager mWifiP2pManager;

    // WiFi Direct通道
    private WifiP2pManager.Channel mChannel;
    // 广播接收器
    private BroadcastReceiver mReceiver;

    // WiFi Direct是否启用
    private boolean isWifiP2pEnabled = false;
    // 热点是否启用
    private boolean isHotspotEnabled = false;
    // 组是否已添加
    private boolean mGroupAdded = false;

    // WiFi Direct组信息
    private WifiP2pGroup mWifiP2pGroup;

    // 连接设备变化监听器
    private OnConnectedDeviceChangedListener mOnConnectedDeviceChangedListener;

    /**
     * 失败监听器接口
     */
    public interface OnFailureListener {
        void onFailure(int failureCode, @Nullable Exception e);
    }

    /**
     * 成功监听器接口
     */
    public interface OnSuccessListener {
        void onSuccess(@NonNull String ssid, @NonNull String password);
    }

    /**
     * 连接设备变化监听器接口
     */
    public interface OnConnectedDeviceChangedListener {
        void onChange(@NonNull List<String> deviceList);
    }

    /**
     * 获取WiFi热点管理器实例
     * @param context 上下文对象
     * @return WiFi热点管理器实例
     */
    public static WiFiApManager getApManager(@NonNull Context context) {
        if (instance == null) {
            synchronized (WiFiApManager.class) {
                if (instance == null) {
                    instance = new WiFiApManager(context);
                }
            }
        }

        return instance;
    }

    /**
     * 构造函数
     * 初始化WiFi管理器和广播接收器
     */
    private WiFiApManager(@NonNull Context context) {
        // 保存上下文引用，用于后续操作
        mContext = context;

        // 获取WiFi管理器实例，用于控制设备WiFi功能
        // 使用getApplicationContext()避免内存泄漏
        mWifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        // 获取WiFi Direct管理器实例，用于创建和管理P2P连接
        mWifiP2pManager = (WifiP2pManager) context.getApplicationContext().getSystemService(Context.WIFI_P2P_SERVICE);

        // 尝试初始化WiFi Direct功能
        if (initP2p()) {
            // 创建意图过滤器，用于注册广播接收器
            IntentFilter intentFilter = new IntentFilter();
            // 添加WiFi Direct状态变化的广播动作，当WiFi Direct开启或关闭时触发
            intentFilter.addAction(WifiP2pManager.WIFI_P2P_STATE_CHANGED_ACTION);
            // 添加对等设备列表变化的广播动作，当发现新设备或设备消失时触发
            intentFilter.addAction(WifiP2pManager.WIFI_P2P_PEERS_CHANGED_ACTION);
            // 添加连接状态变化的广播动作，当P2P连接建立或断开时触发
            intentFilter.addAction(WifiP2pManager.WIFI_P2P_CONNECTION_CHANGED_ACTION);
            // 添加本机设备信息变化的广播动作，当设备名称等信息变化时触发
            intentFilter.addAction(WifiP2pManager.WIFI_P2P_THIS_DEVICE_CHANGED_ACTION);

            // 创建WiFi Direct广播接收器实例，传入当前管理器作为参数
            mReceiver = new WiFiDirectBroadcastReceiver(this);
            // 注册广播接收器，使其能接收上面定义的四种广播
            mContext.registerReceiver(mReceiver, intentFilter);
        } else {
            // 初始化WiFi Direct失败，记录错误日志
            Log.e(TAG, "Failed to init p2p");
        }
    }

    /**
     * 销毁管理器
     * 注销广播接收器并关闭热点
     */
    public void destroy() {
        if (mReceiver != null) {
            mContext.unregisterReceiver(mReceiver);
            mReceiver = null;
        }

        disableWifiApHost();
    }

    /**
     * 关闭WiFi热点
     * 移除当前WiFi Direct组，关闭热点功能
     * 此方法是同步的，防止多线程同时操作热点状态
     */
    public synchronized void disableWifiApHost() {
        // 检查组是否已添加且WiFi Direct通道是否初始化
        if (!mGroupAdded || mChannel == null) {
            return;
        }

        // 移除WiFi Direct组，关闭热点
        mWifiP2pManager.removeGroup(mChannel, new WifiP2pManager.ActionListener() {
            @Override
            public void onSuccess() {
                Log.i(TAG, "removeGroup success");
                // 更新热点状态标志
                isHotspotEnabled = false;
                // 更新组状态标志
                mGroupAdded = false;
                // 更新WiFi Direct状态标志
                isWifiP2pEnabled = false;
            }

            @Override
            public void onFailure(int reason) {
                // 移除组失败，记录失败原因
                // 常见失败原因：
                // 0: ERROR - 操作失败，但没有具体原因
                // 1: P2P_UNSUPPORTED - 设备不支持P2P
                // 2: BUSY - 系统忙，无法处理请求
                Log.e(TAG, "removeGroup failed, reason:" + reason);
            }
        });
    }

    /**
     * 设置WiFi Direct状态
     * 由WiFiDirectBroadcastReceiver在接收到WiFi Direct状态变化广播时调用
     * 用于更新当前WiFi Direct功能的启用状态
     * 
     * @param isWifiP2pEnabled 是否启用WiFi Direct，true表示已启用，false表示已禁用
     */
    public void setIsWifiP2pEnabled(boolean isWifiP2pEnabled) {
        this.isWifiP2pEnabled = isWifiP2pEnabled;
    }

    /**
     * 设置远程设备变化
     * 当收到WiFi Direct对等设备变化广播时调用此方法
     * 获取当前可见的所有WiFi Direct对等设备，并通过监听器通知设备列表变化
     * 需要ACCESS_FINE_LOCATION权限和NEARBY_WIFI_DEVICES权限
     */
    @RequiresPermission(allOf = {Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.NEARBY_WIFI_DEVICES})
    public void setRemotePeersChanged() {
        Log.i(TAG, "setRemotePeersChanged");

        // 请求获取当前可见的所有WiFi Direct对等设备
        mWifiP2pManager.requestPeers(mChannel, peers -> {
            // 创建设备MAC地址列表
            List<String> deviceList = new ArrayList<>();

            // 遍历所有可见的对等设备
            for (WifiP2pDevice device : peers.getDeviceList()) {
                // 确保不添加重复的设备地址
                if (!deviceList.contains(device.deviceAddress)) {
                    deviceList.add(device.deviceAddress);
                }
            }

            // 如果设备变化监听器已设置，则通知设备列表变化
            if (mOnConnectedDeviceChangedListener != null) {
                mOnConnectedDeviceChangedListener.onChange(deviceList);
            }
        });
    }

    /**
     * 设置WiFi Direct热点
     * 创建并配置WiFi Direct热点，设置热点名称、密码和操作频段
     * 需要ACCESS_FINE_LOCATION权限和Android Q及以上版本需要NEARBY_WIFI_DEVICES权限
     * 仅支持Android Q (API 29)及以上版本
     * 
     * @param ssid 热点名称，用于标识WiFi网络
     * @param password 热点密码，用于连接验证
     * @param onSuccessListener 成功监听器，热点创建成功时回调
     * @param onFailureListener 失败监听器，热点创建失败时回调
     */
    @RequiresPermission(allOf = {Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.NEARBY_WIFI_DEVICES})
    @RequiresApi(api = Build.VERSION_CODES.Q)
    public synchronized void setupWifiDirectAp(@NonNull String ssid,
                                               @NonNull String password,
                                               OnSuccessListener onSuccessListener,
                                               OnFailureListener onFailureListener) {
        Log.i(TAG, "setupWifiDirectAp, ssid: " + ssid + ", password: " + password);

        // 检查是否有精确位置权限，没有则直接返回
        if (ActivityCompat.checkSelfPermission(mContext, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            return;
        }

        // 在Android 13 (Tiramisu)及以上版本，检查是否有附近WiFi设备权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU
                && ActivityCompat.checkSelfPermission(mContext, Manifest.permission.NEARBY_WIFI_DEVICES) != PackageManager.PERMISSION_GRANTED) {
            return;
        }

        // 如果组已添加，检查热点是否已启用
        if (mGroupAdded) {
            if (isHotspotEnabled) {
                // 热点已启用，直接回调成功
                onSuccessListener.onSuccess(ssid, password);
            } else {
                // 热点未启用，回调失败
                onFailureListener.onFailure(-1, null);
            }
            return;
        }

        // 检查WiFi Direct通道是否初始化
        if (mChannel == null) {
            Log.e(TAG, "mChannel is null");

            onFailureListener.onFailure(-1, null);
            return;
        }

        /**
         * WiFi信道频率对照表
         * 2.4GHz频段的WiFi信道与频率对应关系
         * 2412 MHz (channel 1) - 最常用的信道之一
         * 2417 MHz (channel 2)
         * 2422 MHz (channel 3)
         * 2427 MHz (channel 4)
         * 2432 MHz (channel 5)
         * 2437 MHz (channel 6) - 最常用的信道之一，干扰较少
         * 2442 MHz (channel 7)
         * 2447 MHz (channel 8)
         * 2452 MHz (channel 9)
         * 2457 MHz (channel 10)
         * 2462 MHz (channel 11) - 北美地区允许使用的最高信道
         * 2467 MHz (channel 12) - 欧洲和亚洲部分地区使用
         * 2472 MHz (channel 13) - 欧洲和亚洲部分地区使用
         * 2484 MHz (channel 14) - 仅日本允许使用，且仅限于802.11b
         */

        // 创建WiFi Direct配置
        WifiP2pConfig config = new WifiP2pConfig.Builder()
                // 设置网络名称(SSID)
                .setNetworkName(ssid)
                // 设置网络密码
                .setPassphrase(password)
                // 禁用持久化模式，避免配置被永久保存
                .enablePersistentMode(false)
                // 设置操作频段为2.4GHz，兼容性更好
                .setGroupOperatingBand(WifiP2pConfig.GROUP_OWNER_BAND_2GHZ)
                // 可以指定特定的操作频率，这里注释掉了，使用默认频率
                //.setGroupOperatingFrequency(2472)
                .build();

        // 创建WiFi Direct组，使设备成为组的拥有者(即热点)
        mWifiP2pManager.createGroup(mChannel, config, new WifiP2pManager.ActionListener() {
            @Override
            public void onSuccess() {
                Log.i(TAG, "success for createGroup");

                // 标记热点已启用
                isHotspotEnabled = true;
                // 回调成功监听器，传递SSID和密码
                onSuccessListener.onSuccess(ssid, password);
            }

            @RequiresPermission(allOf = {Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.NEARBY_WIFI_DEVICES})
            @Override
            public void onFailure(int reason) {
                Log.e(TAG, "Failed to setup wifi direct ap host");

                // 创建组失败时，尝试获取组信息，检查是否已有组存在
                mWifiP2pManager.requestGroupInfo(mChannel, group -> {
                    if (group == null) {
                        // 没有组存在，标记热点未启用并回调失败
                        isHotspotEnabled = false;
                        onFailureListener.onFailure(reason, null);
                    } else {
                        // 已有组存在，标记热点已启用并回调成功
                        isHotspotEnabled = true;
                        onSuccessListener.onSuccess(ssid, password);
                    }
                });
            }
        });

        // 标记组已添加，防止重复创建
        mGroupAdded = true;
    }

    /**
     * 请求已连接设备的MAC地址
     * 获取当前WiFi Direct组中所有客户端设备的MAC地址，并通过监听器回调通知
     * 需要ACCESS_FINE_LOCATION权限和Android Q及以上版本需要NEARBY_WIFI_DEVICES权限
     * 仅支持Android Q (API 29)及以上版本
     * 
     * @param listener 设备变化监听器，用于接收连接设备列表变化的通知
     */
    @RequiresPermission(allOf = {Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.NEARBY_WIFI_DEVICES})
    @RequiresApi(api = Build.VERSION_CODES.Q)
    public synchronized void requestConnectedDeviceMac(@NonNull OnConnectedDeviceChangedListener listener) {
        // 检查热点是否启用且WiFi Direct通道是否初始化
        if (!isHotspotEnabled || mChannel == null) {
            return;
        }

        // 保存监听器引用，用于后续设备变化时通知
        mOnConnectedDeviceChangedListener = listener;

        // 请求WiFi Direct组信息
        mWifiP2pManager.requestGroupInfo(mChannel, wifiP2pGroup -> {
            // 如果当前没有组信息，则使用之前缓存的组信息
            final WifiP2pGroup p2pGroup = wifiP2pGroup == null ? mWifiP2pGroup : wifiP2pGroup;
            if (p2pGroup == null) {
                // 没有有效的组信息，无法获取连接设备
                return;
            }

            // 创建设备MAC地址列表
            List<String> deviceList = new ArrayList<>();
            // 遍历组中的所有客户端设备
            for (WifiP2pDevice client : p2pGroup.getClientList()) {
                // 添加设备MAC地址到列表
                deviceList.add(client.deviceAddress);
            }

            // 通过监听器回调通知设备列表变化
            listener.onChange(deviceList);
            // 缓存当前组信息，用于后续请求
            mWifiP2pGroup = wifiP2pGroup;
        });
    }

    /**
     * 初始化WiFi Direct功能
     * 检查设备是否支持WiFi Direct功能，并初始化WiFi Direct通道
     * 
     * @return 初始化是否成功，true表示成功，false表示失败
     */
    private boolean initP2p() {
        // 检查设备是否支持WiFi Direct功能
        if (!mContext.getPackageManager().hasSystemFeature(PackageManager.FEATURE_WIFI_DIRECT)) {
            Log.e(TAG, "Wi-Fi Direct is not supported by this device.");
            return false;
        }

        // 检查WiFi管理器是否支持P2P功能
        if (!mWifiManager.isP2pSupported()) {
            Log.e(TAG, "Wi-Fi Direct is not supported by the hardware or Wi-Fi is off.");
            return false;
        }

        // 初始化WiFi Direct通道，用于与WiFi Direct服务通信
        // 参数1：上下文对象
        // 参数2：主线程的Looper对象，用于处理回调
        // 参数3：通道监听器，此处为null表示不需要监听通道事件
        mChannel = mWifiP2pManager.initialize(mContext, mContext.getMainLooper(), null);
        if (mChannel == null) {
            Log.e(TAG, "Cannot initialize Wi-Fi Direct.");
            return false;
        }

        return true;
    }

    /**
     * 检查WiFi热点是否已启用
     * 通过Java反射机制调用WifiManager的非公开API方法isWifiApEnabled
     * 注意：由于使用反射调用非公开API，此方法在不同Android版本上可能存在兼容性问题
     * 
     * @return WiFi热点是否启用，true表示已启用，false表示未启用或检查失败
     */
    public boolean isWifiApEnabled() {
        try {
            // 通过反射获取WifiManager类中的isWifiApEnabled方法
            Method method = mWifiManager.getClass().getMethod("isWifiApEnabled");
            // 设置方法可访问，因为isWifiApEnabled是非公开API
            method.setAccessible(true);
            // 调用方法并返回结果
            return (boolean) method.invoke(mWifiManager);
        } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException e) {
            // 捕获可能的异常：方法不存在、调用目标异常、非法访问异常
            e.printStackTrace();
        }

        // 发生异常时返回false
        return false;
    }
}
