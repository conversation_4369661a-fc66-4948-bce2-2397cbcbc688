# SmartView AI交互技术指南

## 目录
- [系统架构概述](#系统架构概述)
- [AI语音交互流程](#ai语音交互流程)
- [AI拍照视频分析流程](#ai拍照视频分析流程)
- [关键技术组件](#关键技术组件)
- [WebSocket通信协议](#websocket通信协议)
- [音频视频处理](#音频视频处理)
- [错误处理和状态管理](#错误处理和状态管理)
- [移植指南](#移植指南)

## 系统架构概述

SmartView是一个基于AI的智能视觉交互系统，采用三层架构：

```
┌─────────────────┐    WiFi热点    ┌─────────────────┐    WebSocket    ┌─────────────────┐
│   Android客户端  │ ◄──────────► │   智能硬件设备   │ ◄──────────► │   云端GPT服务    │
│   (SmartView)   │               │  (SmartSense)   │               │  (Azure/OpenAI) │
└─────────────────┘               └─────────────────┘               └─────────────────┘
```

### 核心组件
- **Android客户端**: 用户界面和控制逻辑
- **智能硬件设备**: 音视频采集和WiFi通信
- **云端GPT服务**: AI语音识别、图像分析和自然语言处理

### 通信流程
1. 客户端通过蓝牙扫描发现智能设备
2. 建立WiFi热点连接
3. 通过WebSocket与设备通信
4. 设备将音视频数据转发到云端GPT服务
5. GPT服务返回AI分析结果

## AI语音交互流程

### 1. 初始化阶段

#### 1.1 蓝牙设备发现
```java
// 位置: SmartViewService.java
// 扫描蓝牙设备，寻找SmartSense设备
private void startBleScan() {
    BluetoothLeScanner scanner = bluetoothAdapter.getBluetoothLeScanner();
    scanner.startScan(scanCallback);
}
```

#### 1.2 WiFi热点建立
```java
// 位置: SmartViewService.java
// 创建WiFi热点供智能设备连接
private void setupWifiAp(String ssid) {
    WifiManager.LocalOnlyHotspotReservation reservation =
        wifiManager.startLocalOnlyHotspot(callback, handler);
}
```

#### 1.3 WebSocket服务器启动
```java
// 位置: SmartViewService.SimpleWebSocketServer
// 启动WebSocket服务器监听设备连接
public SimpleWebSocketServer(int port, AsyncResult result) {
    super(new InetSocketAddress("0.0.0.0", port));
    // 监听端口，等待设备连接
}
```

### 2. 音频流建立阶段

#### 2.1 音频流初始化
```java
// 位置: AudioStreamRunnable.java
// 创建与GPT服务的WebSocket连接
private void initializeAudioStream() {
    mGptBinaryStream = new GptBinaryStream(
        GptBinaryType.BINARY_TYPE_AUDIO,
        Constants.URL_GPT_AUDIO_STREAM,
        mResponseListener,
        true, // 持久模式
        mConversationId
    );
}
```

#### 2.2 音频编码配置
```java
// 位置: Constants.java
public final static int AUDIO_SAMPLE_RATE = 16000; // 16kHz采样率
public final static boolean RESPONSE_AUDIO_MP3 = true; // 使用MP3格式
```

### 3. 实时语音交互阶段

#### 3.1 音频数据采集和发送
```java
// 位置: AudioStreamRunnable.java
// 从设备接收音频数据并发送到GPT
private void handleAudioData(byte[] audioData) {
    if (mGptBinaryStream != null) {
        mGptBinaryStream.sendData(audioData);
    }
}
```

#### 3.2 GPT响应处理
```java
// 位置: AudioStreamRunnable.java
// 处理GPT返回的各种操作指令
switch (action) {
    case GPT_ACTION_QA_QUERY:
        // 处理用户查询
        handleUserQuery((String) params);
        break;
    case GPT_ACTION_QA_ANSWER:
        // 处理AI回答
        handleAIResponse((String) params);
        break;
    case GPT_ACTION_QA_BINARY:
        // 处理音频回答
        handleAudioResponse((byte[]) params);
        break;
}
```

#### 3.3 音频播放
```java
// 位置: GptResponseAudio.java
// 播放GPT返回的音频回答
public void doReceivedAudioData(byte[] data) {
    if (mAudioMp3 && mAudioDecoder != null) {
        mAudioDecoder.queue(data); // MP3解码播放
    } else {
        mAudioTrack.write(data, 0, data.length); // PCM直接播放
    }
}
```

### 4. 用户模式切换

#### 4.1 支持的用户模式
```java
// 位置: GptAction.java
public enum GptAction {
    GPT_ACTION_SWITCH_MODE_GENERAL,    // 通用模式
    GPT_ACTION_SWITCH_MODE_BLIND,      // 盲人模式
    GPT_ACTION_SWITCH_MODE_ELDER,      // 老年人模式
    GPT_ACTION_SWITCH_MODE_TRAVELING,  // 旅行模式
    GPT_ACTION_SWITCH_MODE_NATURALIST, // 自然观察模式
    GPT_ACTION_SWITCH_MODE_TRANSLATION,// 翻译模式
    GPT_ACTION_SWITCH_MODE_MEETING,    // 会议模式
    GPT_ACTION_SWITCH_MODE_HEALTH      // 健康模式
}
```

#### 4.2 模式切换处理
```java
// 位置: AudioStreamRunnable.java
// 根据GPT指令切换用户模式
case GPT_ACTION_SWITCH_MODE_GENERAL:
case GPT_ACTION_SWITCH_MODE_BLIND:
    // 发送模式切换消息到UI
    Bundle data = new Bundle();
    data.putInt("user_mode", getUserModeByAction(action).ordinal());
    sendMessageToUI(Constants.EVENT_GPT_SWITCH_MODE, data);
    break;
```

## AI拍照视频分析流程

### 1. 图像捕获阶段

#### 1.1 拍照指令处理
```java
// 位置: AudioStreamRunnable.java
// GPT发出拍照指令
case GPT_ACTION_TAKE_PHOTO:
    sendMessageToUI(Constants.EVENT_GPT_TAKE_PHOTO, null);
    break;
```

#### 1.2 图像数据获取
```java
// 位置: HomeFragment.java
// 请求设备捕获图像
private void requestOneImage() {
    if (mMessenger != null) {
        Message msg = Message.obtain();
        msg.what = Constants.EVENT_CAPTURE_IMAGE;
        messenger.send(msg);
    }
}
```

### 2. 图像处理和上传阶段

#### 2.1 图像格式转换
```java
// 位置: BitmapToJPEG.java
// 将Bitmap转换为JPEG格式
public static void convert(Bitmap bitmap, OutputStream outputStream) {
    bitmap.compress(Bitmap.CompressFormat.JPEG, 85, outputStream);
}
```

#### 2.2 图像数据上传
```java
// 位置: VideoStreamRunnable.java
// 发送图像到GPT进行分析
private void handleImage() {
    byte[] imageData = queue.poll(Constants.DEFAULT_IMAGE_TIMEOUT, TimeUnit.SECONDS);
    if (imageData != null) {
        Bitmap bitmap = BitmapFactory.decodeByteArray(imageData, 0, imageData.length);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        BitmapToJPEG.convert(bitmap, outputStream);
        mGptBinaryStream.sendData(outputStream.toByteArray());
    }
}
```

### 3. 视频录制和分析阶段

#### 3.1 视频录制控制
```java
// 位置: AudioStreamRunnable.java
// 处理视频录制指令
case GPT_ACTION_TAKE_VIDEO:
case GPT_ACTION_VIDEO_START:
case GPT_ACTION_VIDEO_STOP:
    Bundle videoData = new Bundle();
    videoData.putInt("duration", duration);
    sendMessageToUI(Constants.EVENT_GPT_TAKE_VIDEO, videoData);
    break;
```

#### 3.2 视频流处理
```java
// 位置: VideoStreamRunnable.java
// 处理实时视频流
private void handleVideo() {
    while (!mStopVideoStream) {
        byte[] imageData = queue.poll(timeout, TimeUnit.SECONDS);
        if (imageData != null) {
            // 每秒处理一帧
            if (firstFrame || curSecond > second) {
                Bitmap bitmap = BitmapFactory.decodeByteArray(imageData, 0, imageData.length);
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                BitmapToJPEG.convert(bitmap, outputStream);
                mGptBinaryStream.sendData(outputStream.toByteArray());
            }
        }
    }
}
```

### 4. AI分析结果处理

#### 4.1 文本结果显示
```java
// 位置: HomeFragment.java
// 显示AI分析的文本结果
private void appendQAText(String text, boolean isQuery, String convId) {
    MessageListAdapter.MessageItem item = new MessageListAdapter.MessageItem();
    item.text = text;
    item.isFromUser = isQuery;
    item.timestamp = System.currentTimeMillis();
    mMessageAdapter.addMessage(item);
}
```

#### 4.2 图像结果显示
```java
// 位置: HomeFragment.java
// 显示处理后的图像和分析时间
private void showImage(Bitmap bitmap, long processTime, boolean updateText, boolean bubbleShow) {
    mBinding.imageView.setImageBitmap(bitmap);
    if (updateText) {
        mBinding.textProcessTime.setText(String.format(Locale.getDefault(),
            "处理时间: %d ms", processTime));
    }
}
```

## 关键技术组件

### 1. WebSocket通信组件

#### 1.1 GptBinaryStream - GPT二进制流处理
```java
// 位置: GptBinaryStream.java
// 核心WebSocket通信类，负责与GPT服务建立连接
public class GptBinaryStream implements GptStream {
    private final GptBinaryType mGptBinaryType; // 数据类型(音频/图像)
    private final String mUrl;                   // WebSocket服务器地址
    private final GptResponseListener mResponseListener; // 响应监听器
    private final boolean mPersistentMode;       // 是否持久连接
    private WebSocket mWebSocket;                // WebSocket连接实例

    // 发送二进制数据到GPT服务
    public boolean send(byte[] data) {
        if (data == null || data.length == 0) return false;

        // 图像模式下防止队列过大
        if (mGptBinaryType == GptBinaryType.BINARY_TYPE_IMAGE
            && mWebSocket.queueSize() >= data.length * 5L) {
            mWebSocket.cancel(); // 清空队列
        }

        return mWebSocket.send(ByteString.of(data));
    }
}
```

#### 1.2 WebSocket连接建立
```java
// 位置: GptBinaryStream.java
// 建立安全的WebSocket连接
private void connect() {
    String key = RandomUtils.getRandomString();
    Request request = new Request.Builder()
        .url(mUrl)
        .addHeader("key", key)
        .addHeader("hash", MD5Utils.getMD5(key + Constants.GPT_SALT)) // 安全验证
        .addHeader("conv_id", mConversationId)
        .addHeader("client", "SmartView-GGEC")
        .addHeader("audio_mp3", Constants.RESPONSE_AUDIO_MP3 ? "1" : "0")
        .addHeader("persistent_mode", mPersistentMode ? "1" : "0")
        .build();

    mWebSocket = mOkHttpClient.newWebSocket(request, mWebSocketListener);
}
```

### 2. 音频处理组件

#### 2.1 AudioEncoder - 音频编码器
```java
// 位置: AudioEncoder.java
// 将PCM音频编码为AAC格式
public class AudioEncoder {
    private static final int SAMPLE_RATE = Constants.AUDIO_SAMPLE_RATE; // 16kHz
    private static final int CHANNEL_COUNT = 1;    // 单声道
    private static final int BIT_RATE = 32000;     // 比特率

    // 启动编码过程
    public void startEncoding(File outFile) {
        // 创建AAC编码器
        mMediaCodec = MediaCodec.createEncoderByType(MediaFormat.MIMETYPE_AUDIO_AAC);

        // 配置编码参数
        MediaFormat mediaFormat = MediaFormat.createAudioFormat(
            MediaFormat.MIMETYPE_AUDIO_AAC, SAMPLE_RATE, CHANNEL_COUNT);
        mediaFormat.setInteger(MediaFormat.KEY_BIT_RATE, BIT_RATE);
        mediaFormat.setInteger(MediaFormat.KEY_AAC_PROFILE,
            MediaCodecInfo.CodecProfileLevel.AACObjectLC);

        mMediaCodec.configure(mediaFormat, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);
        mMediaCodec.start();
    }

    // 添加ADTS头信息到AAC数据包
    private void addADTStoPacket(byte[] packet, int packetLen) {
        int profile = 2;  // AAC LC
        int freqIdx = 8;  // 16KHz
        int chanCfg = 1;  // 单声道

        packet[0] = (byte) 0xFF;
        packet[1] = (byte) 0xF1;
        packet[2] = (byte) (((profile - 1) << 6) + (freqIdx << 2) + (chanCfg >> 2));
        // ... 其他ADTS头字段
    }
}
```

#### 2.2 AudioDecoder - 音频解码器
```java
// 位置: AudioDecoder.java
// MP3音频解码播放
public class AudioDecoder {
    private MediaCodec mMediaCodec;
    private AudioTrack mAudioTrack;

    public void startDecoding() {
        // 创建MP3解码器
        mMediaCodec = MediaCodec.createDecoderByType(MediaFormat.MIMETYPE_AUDIO_MPEG);

        // 配置解码器
        MediaFormat format = new MediaFormat();
        format.setString(MediaFormat.KEY_MIME, MediaFormat.MIMETYPE_AUDIO_MPEG);
        mMediaCodec.configure(format, null, null, 0);
        mMediaCodec.start();
    }
}
```

#### 2.3 GptResponseAudio - GPT音频响应处理
```java
// 位置: GptResponseAudio.java
// 处理GPT返回的音频数据
public class GptResponseAudio {
    private AudioDecoder mAudioDecoder;
    private AudioTrack mAudioTrack;
    private boolean mAudioMp3;

    // 处理接收到的音频数据
    public void doReceivedAudioData(byte[] data) {
        if (!isPlaying) {
            mAudioTrack.play();
            isPlaying = true;
        }

        if (mAudioMp3 && mAudioDecoder != null) {
            mAudioDecoder.queue(data); // MP3解码播放
        } else {
            mAudioTrack.write(data, 0, data.length); // PCM直接播放
        }
    }

    // 创建AudioTrack
    private void createStreamModeAudioTrack() {
        int bufferSize = AudioTrack.getMinBufferSize(
            Constants.AUDIO_SAMPLE_RATE,
            AudioFormat.CHANNEL_OUT_MONO,
            AudioFormat.ENCODING_PCM_16BIT);

        mAudioTrack = new AudioTrack.Builder()
            .setAudioAttributes(new AudioAttributes.Builder()
                .setUsage(AudioAttributes.USAGE_MEDIA)
                .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                .build())
            .setAudioFormat(new AudioFormat.Builder()
                .setEncoding(AudioFormat.ENCODING_PCM_16BIT)
                .setSampleRate(Constants.AUDIO_SAMPLE_RATE)
                .setChannelMask(AudioFormat.CHANNEL_OUT_MONO)
                .build())
            .setBufferSizeInBytes(bufferSize)
            .setTransferMode(AudioTrack.MODE_STREAM)
            .build();
    }
}
```

### 3. 视频处理组件

#### 3.1 VideoEncoder - 视频编码器
```java
// 位置: VideoEncoder.java
// H.264视频编码
public class VideoEncoder {
    private MediaCodec mMediaCodec;
    private MediaMuxer mMediaMuxer;

    public void startEncoding(int width, int height, File outputFile) {
        // 创建H.264编码器
        mMediaCodec = MediaCodec.createEncoderByType(MediaFormat.MIMETYPE_VIDEO_AVC);

        // 配置编码参数
        MediaFormat format = MediaFormat.createVideoFormat(MediaFormat.MIMETYPE_VIDEO_AVC, width, height);
        format.setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface);
        format.setInteger(MediaFormat.KEY_BIT_RATE, 2000000); // 2Mbps
        format.setInteger(MediaFormat.KEY_FRAME_RATE, 30);
        format.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, 1);

        mMediaCodec.configure(format, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);
    }
}
```

#### 3.2 BitmapToJPEG - 图像格式转换
```java
// 位置: BitmapToJPEG.java
// 高效的Bitmap到JPEG转换
public class BitmapToJPEG {
    public static void convert(Bitmap bitmap, OutputStream outputStream) {
        // 使用85%质量压缩，平衡文件大小和图像质量
        bitmap.compress(Bitmap.CompressFormat.JPEG, 85, outputStream);
    }

    // 带尺寸限制的转换
    public static void convertWithSizeLimit(Bitmap bitmap, OutputStream outputStream, int maxSize) {
        int quality = 85;
        do {
            ByteArrayOutputStream tempStream = new ByteArrayOutputStream();
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, tempStream);
            if (tempStream.size() <= maxSize) {
                outputStream.write(tempStream.toByteArray());
                break;
            }
            quality -= 5; // 降低质量重试
        } while (quality > 10);
    }
}
```

## WebSocket通信协议

### 1. 连接建立协议

#### 1.1 音频流连接
```
URL: wss://aihardware-demo.orangepebble-30028f23.southeastasia.azurecontainerapps.io/ws

Headers:
- key: [随机字符串]
- hash: [MD5(key + salt)]
- conv_id: [会话ID，音频流为空]
- client: "SmartView-GGEC"
- audio_mp3: "1" (使用MP3格式) / "0" (使用PCM格式)
- persistent_mode: "1" (持久连接) / "0" (单次连接)
```

#### 1.2 图像流连接
```
URL: wss://aihardware-demo.orangepebble-30028f23.southeastasia.azurecontainerapps.io/images

Headers:
- key: [随机字符串]
- hash: [MD5(key + salt)]
- conv_id: [会话ID]
- client: "SmartView-GGEC"
- persistent_mode: "1"
- Set-Cookie: [认证Cookie]
```

### 2. 数据传输协议

#### 2.1 音频数据格式
```
数据类型: 二进制 (Binary)
音频格式: PCM 16-bit 单声道
采样率: 16kHz
数据包大小: 可变长度
传输方式: 实时流式传输
```

#### 2.2 图像数据格式
```
数据类型: 二进制 (Binary)
图像格式: JPEG
压缩质量: 85%
最大尺寸: 无限制 (但建议控制在合理范围)
传输方式: 单帧或视频流
```

### 3. 响应处理协议

#### 3.1 GPT操作指令
```java
// 位置: GptAction.java
// GPT服务返回的操作指令类型
public enum GptAction {
    // 模式切换指令
    GPT_ACTION_SWITCH_MODE_GENERAL,
    GPT_ACTION_SWITCH_MODE_BLIND,
    GPT_ACTION_SWITCH_MODE_ELDER,

    // 媒体操作指令
    GPT_ACTION_TAKE_PHOTO,
    GPT_ACTION_TAKE_VIDEO,
    GPT_ACTION_VIDEO_START,
    GPT_ACTION_VIDEO_STOP,

    // 问答操作指令
    GPT_ACTION_QA_BINARY,    // 音频回答
    GPT_ACTION_QA_QUERY,     // 文本查询
    GPT_ACTION_QA_ANSWER,    // 文本回答

    // 状态指令
    GPT_ACTION_PROCESSING,   // 处理中
    GPT_ACTION_CONNECTED,    // 已连接
    GPT_ACTION_COMPLETE,     // 完成
}
```

#### 3.2 响应数据解析
```java
// 位置: AudioStreamRunnable.java
// 解析GPT返回的响应数据
private void handleGptResponse(GptAction action, Object params) {
    switch (action) {
        case GPT_ACTION_QA_QUERY:
            // params为String类型，包含用户查询文本
            String query = (String) params;
            sendToUI(Constants.EVENT_GPT_QA_QUERY, query);
            break;

        case GPT_ACTION_QA_ANSWER:
            // params为String类型，包含AI回答文本
            String answer = (String) params;
            sendToUI(Constants.EVENT_GPT_QA_ANSWER, answer);
            break;

        case GPT_ACTION_QA_BINARY:
            // params为byte[]类型，包含音频数据
            byte[] audioData = (byte[]) params;
            playAudioResponse(audioData);
            break;

        case GPT_ACTION_CONNECTED:
            // params为String类型，包含会话ID
            mCurrentConversationId = (String) params;
            break;
    }
}
```

## 音频视频处理

### 1. 音频处理流水线

#### 1.1 音频采集链路
```
智能设备麦克风 → PCM数据 → WebSocket传输 → Android客户端 → GPT服务
                ↓
            16kHz采样率
            16-bit精度
            单声道
```

#### 1.2 音频播放链路
```
GPT服务 → MP3/PCM数据 → Android客户端 → AudioDecoder → AudioTrack → 扬声器
                                        ↓
                                   实时解码播放
```

#### 1.3 音频编码配置
```java
// 位置: AudioEncoder.java
// 关键音频编码参数
private static final int SAMPLE_RATE = 16000;    // 采样率
private static final int CHANNEL_COUNT = 1;      // 单声道
private static final int BIT_RATE = 32000;       // 比特率32kbps
private static final int INPUT_BUFFER = 512 * 1024; // 输入缓冲区512KB

// AAC编码配置
MediaFormat mediaFormat = MediaFormat.createAudioFormat(
    MediaFormat.MIMETYPE_AUDIO_AAC, SAMPLE_RATE, CHANNEL_COUNT);
mediaFormat.setInteger(MediaFormat.KEY_BIT_RATE, BIT_RATE);
mediaFormat.setInteger(MediaFormat.KEY_AAC_PROFILE,
    MediaCodecInfo.CodecProfileLevel.AACObjectLC);
mediaFormat.setInteger(MediaFormat.KEY_CHANNEL_MASK, AudioFormat.CHANNEL_IN_MONO);
```

### 2. 视频处理流水线

#### 2.1 图像采集链路
```
智能设备摄像头 → JPEG图像 → WebSocket传输 → Android客户端 → GPT服务
                  ↓
              可配置分辨率
              85%压缩质量
```

#### 2.2 视频录制链路
```
实时图像流 → VideoEncoder → H.264编码 → MP4文件 → 本地存储/上传分析
             ↓
         30fps帧率
         2Mbps比特率
```

#### 2.3 支持的分辨率配置
```java
// 位置: HomeFragment.java
// 可选的视频分辨率
private final static String[] FrameSizeItems = {
    "96X96",           // 最小分辨率，适合快速传输
    "QQVGA_160x120",   // 超低分辨率
    "QCIF_176x144",    // 标准低分辨率
    "HQVGA_240x176",   // 中低分辨率
    "QVGA_240x240",    // 方形中分辨率
    "CIF_400x296",     // 中分辨率
    "HVGA_480x320",    // 中高分辨率
    "VGA_640x480",     // 标准分辨率
    "SVGA_800x600",    // 高分辨率
    "XGA_1024x768",    // 超高分辨率
    "HD_1280x720",     // 高清分辨率
    "SXGA_1280x1024",  // 超高清分辨率
    "UXGA_1600x1200"   // 最高分辨率
};
```

### 3. 性能优化策略

#### 3.1 内存管理
```java
// 位置: VideoStreamRunnable.java
// 及时释放Bitmap内存
if (bitmap != null) {
    // 处理完成后立即回收
    bitmap.recycle();
}

// 位置: GptBinaryStream.java
// 控制WebSocket队列大小防止内存溢出
if (mGptBinaryType == GptBinaryType.BINARY_TYPE_IMAGE
    && mWebSocket.queueSize() >= data.length * 5L) {
    mWebSocket.cancel(); // 清空队列
}
```

#### 3.2 线程池管理
```java
// 位置: HomeFragment.java
// 高优先级线程池处理音视频数据
private final static ExecutorService mThreadPool = Executors.newFixedThreadPool(4, r -> {
    Thread t = new Thread(r);
    t.setPriority(Thread.MAX_PRIORITY); // 最高优先级
    return t;
});
```

#### 3.3 数据压缩优化
```java
// 位置: BitmapToJPEG.java
// 自适应质量压缩
public static void convertWithSizeLimit(Bitmap bitmap, OutputStream outputStream, int maxSize) {
    int quality = 85;
    do {
        ByteArrayOutputStream tempStream = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.JPEG, quality, tempStream);
        if (tempStream.size() <= maxSize) {
            outputStream.write(tempStream.toByteArray());
            break;
        }
        quality -= 5; // 逐步降低质量
    } while (quality > 10);
}
```

## 错误处理和状态管理

### 1. 连接错误处理

#### 1.1 WebSocket连接失败
```java
// 位置: GptBinaryStream.java
// WebSocket连接监听器
private final WebSocketListener mWebSocketListener = new WebSocketListener() {
    @Override
    public void onFailure(WebSocket webSocket, Throwable t, Response response) {
        Log.e(TAG, "WebSocket connection failed: " + t.getMessage());
        // 通知UI显示错误状态
        mResponseListener.onAction(GptAction.GPT_ACTION_ERROR, null);

        // 尝试重连
        if (mRetryCount < MAX_RETRY_COUNT) {
            mRetryCount++;
            scheduleReconnect();
        }
    }

    @Override
    public void onClosed(WebSocket webSocket, int code, String reason) {
        Log.i(TAG, "WebSocket closed: " + code + " " + reason);
        if (!mManualClose) {
            // 非手动关闭，尝试重连
            scheduleReconnect();
        }
    }
};
```

#### 1.2 设备连接超时处理
```java
// 位置: SmartViewService.java
// 心跳检测机制
private void startHeartbeatCheck() {
    mHeartbeatTimer = new Timer();
    mHeartbeatTimer.scheduleAtFixedRate(new TimerTask() {
        @Override
        public void run() {
            long currentTime = SystemClock.elapsedRealtime();
            if (currentTime - lastPingTimestamp > HEARTBEAT_TIMEOUT) {
                // 心跳超时，断开连接
                Log.w(TAG, "Device heartbeat timeout");
                disconnectDevice();
            }
        }
    }, HEARTBEAT_DELAY, HEARTBEAT_DELAY);
}
```

### 2. 音视频处理错误

#### 2.1 音频编解码错误
```java
// 位置: AudioEncoder.java
// 编码错误处理
private void encode() {
    try {
        // 编码逻辑
        int inputBufferIndex = mMediaCodec.dequeueInputBuffer(TIMEOUT);
        if (inputBufferIndex >= 0) {
            // 处理输入缓冲区
        }
    } catch (Exception e) {
        Log.e(TAG, "Audio encoding error: " + e.getMessage());
        // 停止编码并通知回调
        mAbort = true;
        release();
    }
}
```

#### 2.2 图像处理错误
```java
// 位置: VideoStreamRunnable.java
// 图像处理异常捕获
private void handleImage() {
    try {
        byte[] imageData = queue.poll(Constants.DEFAULT_IMAGE_TIMEOUT, TimeUnit.SECONDS);
        if (imageData != null) {
            Bitmap bitmap = BitmapFactory.decodeByteArray(imageData, 0, imageData.length);
            if (bitmap == null) {
                Log.e(TAG, "Failed to decode image data");
                notifyStreamingError();
                return;
            }
            // 处理图像
        } else {
            Log.w(TAG, "Image capture timeout");
            notifyStreamingError();
        }
    } catch (Exception e) {
        Log.e(TAG, "Image processing error: " + e.getMessage());
        notifyStreamingError();
    }
}
```

### 3. 状态管理机制

#### 3.1 UI状态同步
```java
// 位置: HomeFragment.java
// 状态管理方法
private void toggleProcessingStatus(boolean processing) {
    if (processing) {
        mBinding.progressBar.setVisibility(View.VISIBLE);
        mBinding.statusText.setText("AI处理中...");
    } else {
        mBinding.progressBar.setVisibility(View.GONE);
        mBinding.statusText.setText("就绪");
    }
}

private void toggleErrorAlert(boolean show) {
    if (show) {
        mBinding.errorIcon.setVisibility(View.VISIBLE);
        mBinding.statusText.setText("连接错误");
        mBinding.statusText.setTextColor(Color.RED);
    } else {
        mBinding.errorIcon.setVisibility(View.GONE);
        mBinding.statusText.setTextColor(Color.BLACK);
    }
}
```

#### 3.2 设备状态监控
```java
// 位置: HomeFragment.java
// 设备状态更新
private void updateConnectionStatus(boolean connected) {
    mConnected = connected;
    if (connected) {
        mBinding.deviceStatus.setText("已连接");
        mBinding.deviceStatus.setTextColor(Color.GREEN);
        mBinding.deviceIcon.setImageResource(R.drawable.ic_device_online);
    } else {
        mBinding.deviceStatus.setText("未连接");
        mBinding.deviceStatus.setTextColor(Color.RED);
        mBinding.deviceIcon.setImageResource(R.drawable.ic_device_offline);
    }
}
```

## 移植指南

### 1. 核心依赖和配置

#### 1.1 必需的Android权限
```xml
<!-- 位置: AndroidManifest.xml -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
<uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

#### 1.2 关键第三方库
```gradle
// 位置: app/build.gradle
dependencies {
    // WebSocket通信
    implementation 'com.squareup.okhttp3:okhttp:4.9.3'

    // JSON处理
    implementation 'org.json:json:20210307'

    // RxJava异步处理
    implementation 'io.reactivex.rxjava2:rxjava:2.2.21'
    implementation 'io.reactivex.rxjava2:rxandroid:2.1.1'

    // 视图绑定
    implementation 'androidx.databinding:databinding-runtime:7.0.4'

    // RecyclerView
    implementation 'androidx.recyclerview:recyclerview:1.2.1'
}
```

### 2. 移植步骤

#### 2.1 复制核心组件
1. **GPT通信模块**
   - `gpt/` 包下的所有类
   - `GptBinaryStream.java` - WebSocket通信核心
   - `GptAction.java` - 操作指令枚举
   - `GptResponseListener.java` - 响应监听接口

2. **音视频处理模块**
   - `media/` 包下的所有类
   - `AudioEncoder.java` - 音频编码
   - `AudioDecoder.java` - 音频解码
   - `VideoEncoder.java` - 视频编码
   - `BitmapToJPEG.java` - 图像转换

3. **流处理模块**
   - `stream/` 包下的所有类
   - `AudioStreamRunnable.java` - 音频流处理
   - `VideoStreamRunnable.java` - 视频流处理
   - `GptResponseAudio.java` - 音频播放

#### 2.2 配置常量
```java
// 位置: Constants.java
// 根据实际情况修改以下配置
public final class Constants {
    // GPT服务地址 - 需要替换为实际的服务地址
    public final static String URL_GPT_AUDIO_STREAM = "wss://your-gpt-service.com/ws";
    public final static String URL_GPT_VIDEO_STREAM = "wss://your-gpt-service.com/images";

    // 音频配置 - 可根据需要调整
    public final static int AUDIO_SAMPLE_RATE = 16000;
    public final static boolean RESPONSE_AUDIO_MP3 = true;

    // 安全配置 - 需要设置实际的盐值
    public static String GPT_SALT = "your-security-salt";
}
```

#### 2.3 集成到现有项目

1. **创建服务类**
```java
// 创建类似SmartViewService的后台服务
public class AIInteractionService extends Service {
    // 实现蓝牙扫描、WiFi热点、WebSocket服务器功能
    // 参考SmartViewService的实现
}
```

2. **创建UI控制器**
```java
// 创建类似HomeFragment的UI控制类
public class AIInteractionFragment extends Fragment {
    // 实现音视频流控制、状态显示、消息处理
    // 参考HomeFragment的实现
}
```

3. **实现消息处理**
```java
// 实现类似UiEventHandler的消息处理机制
private final class AIEventHandler extends Handler {
    @Override
    public void handleMessage(Message msg) {
        // 处理各种AI交互事件
        // 参考HomeFragment.UiEventHandler的实现
    }
}
```

### 3. 自定义和扩展

#### 3.1 添加新的用户模式
```java
// 位置: GptAction.java
// 在枚举中添加新模式
public enum GptAction {
    // 现有模式...

    // 新增自定义模式
    GPT_ACTION_SWITCH_MODE_CUSTOM1,
    GPT_ACTION_SWITCH_MODE_CUSTOM2,
}

// 位置: AudioStreamRunnable.java
// 在switch语句中添加处理逻辑
case GPT_ACTION_SWITCH_MODE_CUSTOM1:
    // 处理自定义模式1
    break;
```

#### 3.2 修改音视频参数
```java
// 位置: AudioEncoder.java
// 根据需要调整音频编码参数
private static final int SAMPLE_RATE = 22050;    // 提高采样率
private static final int BIT_RATE = 64000;       // 提高比特率

// 位置: VideoEncoder.java
// 根据需要调整视频编码参数
format.setInteger(MediaFormat.KEY_BIT_RATE, 4000000); // 4Mbps
format.setInteger(MediaFormat.KEY_FRAME_RATE, 60);    // 60fps
```

#### 3.3 集成其他AI服务
```java
// 创建新的AI服务适配器
public class CustomAIService implements GptStream {
    @Override
    public boolean sendData(byte[] data) {
        // 实现与其他AI服务的通信
        return customAIClient.send(data);
    }

    @Override
    public void close() {
        // 关闭连接
        customAIClient.disconnect();
    }
}
```

### 4. 注意事项和最佳实践

#### 4.1 性能优化
- 使用高优先级线程处理音视频数据
- 及时释放Bitmap和MediaCodec资源
- 控制WebSocket队列大小防止内存溢出
- 使用对象池减少GC压力

#### 4.2 错误处理
- 实现完善的重连机制
- 添加超时处理逻辑
- 提供用户友好的错误提示
- 记录详细的错误日志

#### 4.3 安全考虑
- 使用HTTPS/WSS加密传输
- 实现请求签名验证
- 避免在日志中输出敏感信息
- 定期更新安全配置

#### 4.4 用户体验
- 提供清晰的状态反馈
- 实现流畅的UI交互
- 支持离线模式
- 优化启动和响应时间

通过遵循本指南，可以成功将SmartView的AI交互功能移植到其他Android项目中，并根据具体需求进行定制和扩展。
