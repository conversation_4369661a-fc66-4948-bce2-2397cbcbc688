<?xml version="1.0" encoding="utf-8"?>
<!-- 
    发送消息气泡背景
    通过layer-list和各种形状组合创建一个带有尾部的聊天气泡
    使用主要颜色作为气泡背景色
    气泡尾部位于右下方，表示这是发送出去的消息
-->
<layer-list xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools" >

    <!-- 气泡的主体矩形部分 -->
    <item
        android:left="@dimen/speech_bubble_corners"
        android:right="@dimen/speech_bubble_corners_plus_tail"
        android:bottom="@dimen/speech_bubble_spacing"
        tools:width="50dp"
        tools:height="50dp">
        <shape android:shape="rectangle">
            <solid android:color="?attr/colorPrimary"/>
        </shape>
    </item>

    <!-- 左侧矩形填充 -->
    <item
        android:top="@dimen/speech_bubble_corners"
        android:bottom="@dimen/speech_bubble_corners_plus_tail"
        android:gravity="left"
        android:width="@dimen/speech_bubble_corners">
        <shape android:shape="rectangle">
            <solid android:color="?attr/colorPrimary"/>
        </shape>
    </item>

    <!-- 右侧矩形填充 -->
    <item
        android:top="@dimen/speech_bubble_corners"
        android:bottom="@dimen/speech_bubble_corners_plus_tail"
        android:right="@dimen/speech_bubble_spacing"
        android:gravity="right"
        android:width="@dimen/speech_bubble_corners">
        <shape android:shape="rectangle">
            <solid android:color="?attr/colorPrimary"/>
        </shape>
    </item>

    <!-- 左下角圆角 -->
    <item
        android:width="@dimen/speech_bubble_corners"
        android:height="@dimen/speech_bubble_corners"
        android:bottom="@dimen/speech_bubble_spacing"
        android:gravity="bottom|left">
        <vector xmlns:android="http://schemas.android.com/apk/res/android"
            android:width="@dimen/speech_bubble_corners"
            android:height="@dimen/speech_bubble_corners"
            android:viewportWidth="10.0"
            android:viewportHeight="10.0">
            <path
                android:pathData="M0,0 A10,10 0 0,0 10,10 L10,0 Z"
                android:fillColor="?attr/colorPrimary"/>
        </vector>
    </item>

    <!-- 右上角圆角 -->
    <item
        android:width="@dimen/speech_bubble_corners"
        android:height="@dimen/speech_bubble_corners"
        android:right="@dimen/speech_bubble_spacing"
        android:gravity="top|right">
        <vector xmlns:android="http://schemas.android.com/apk/res/android"
            android:width="@dimen/speech_bubble_corners"
            android:height="@dimen/speech_bubble_corners"
            android:viewportWidth="10.0"
            android:viewportHeight="10.0">
            <path
                android:pathData="M10,10 A10,10 0 0,0 0,0 L0,10 Z"
                android:fillColor="?attr/colorPrimary"/>
        </vector>
    </item>

    <!-- 左上角圆角 -->
    <item
        android:width="@dimen/speech_bubble_corners"
        android:height="@dimen/speech_bubble_corners"
        android:gravity="top|left">
        <vector xmlns:android="http://schemas.android.com/apk/res/android"
            android:width="@dimen/speech_bubble_corners"
            android:height="@dimen/speech_bubble_corners"
            android:viewportWidth="10.0"
            android:viewportHeight="10.0">
            <path
                android:pathData="M10,0 A10,10 0 0,0 0,10 L10,10 Z"
                android:fillColor="?attr/colorPrimary"/>
        </vector>
    </item>

    <!-- 右下角带尾部的形状，这是气泡的尾巴部分 -->
    <item
        android:width="@dimen/speech_bubble_corners_plus_tail"
        android:height="@dimen/speech_bubble_corners_plus_tail"
        android:gravity="bottom|right">
        <vector xmlns:android="http://schemas.android.com/apk/res/android"
            android:width="@dimen/speech_bubble_corners_plus_tail"
            android:height="@dimen/speech_bubble_corners_plus_tail"
            android:viewportWidth="150.0"
            android:viewportHeight="150.0">
            <path
                android:pathData="M0,100 C7.67309143,100 14.1935201,100.346373 20.500756,99.0996492 C43.6628959,129.872031 94.1698247,146.306561 150.320843,150.792562 C113.168693,130.799632 87.2808993,98.5054948 81.0808824,68.6524321 C94.1277117,51.7595331 100,23.9957121 100,0 L0,0 L0,100 Z"
                android:fillColor="?attr/colorPrimary"/>
        </vector>
    </item>

</layer-list>