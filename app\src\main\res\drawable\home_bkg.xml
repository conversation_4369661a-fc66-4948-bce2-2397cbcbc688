<?xml version="1.0" encoding="utf-8"?>
<!-- 
    主页背景矢量图
    创建一个简单的图形作为主页背景
    包含一个黑色矩形和白色线条组成的图案
-->
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="200dp"
    android:height="200dp"
    android:viewportWidth="200"
    android:viewportHeight="200">

    <!-- 黑色矩形背景 -->
    <path
        android:fillColor="#000000"
        android:pathData="M10,10 L190,10 L190,190 L10,190 Z"/>

    <!-- 水平线条 -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M50,100 L150,100 M50,120 L150,120"
        android:strokeColor="#000000"
        android:strokeWidth="4"/>

    <!-- 交叉线条 -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M85,70 L115,130 M85,130 L115,70"
        android:strokeColor="#000000"
        android:strokeWidth="4"/>
</vector>
