package com.microsoft.mai.smartview.utils;

import android.annotation.SuppressLint;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

/**
 * 随机数工具类
 * 提供生成随机字符串和时间戳的功能
 */
public class RandomUtils {

    /**
     * 生成随机字符串
     * 由当前时间戳和UUID组合而成
     * @return 生成的随机字符串
     */
    public static String getRandomString() {
        String timeString = getCurrentTime();
        UUID uuid = UUID.randomUUID();
        String randomString = uuid.toString().replace("-", "").substring(0, 8);

        return timeString + randomString ;
    }

    /**
     * 获取当前时间戳
     * 格式为：yyyyMMddHHmmssSSS
     * @return 当前时间戳字符串
     */
    public static String getCurrentTime() {
        @SuppressLint("SimpleDateFormat")
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        return sdf.format(new Date());
    }
}
